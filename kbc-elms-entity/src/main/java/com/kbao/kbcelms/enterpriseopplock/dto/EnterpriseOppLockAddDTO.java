package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 企业机会锁定新增DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定新增DTO")
public class EnterpriseOppLockAddDTO {
    
    /** 机会id */
    @ApiModelProperty(value = "机会ID", example = "1001", required = true)
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称", example = "某某有限公司", required = true)
    @NotNull(message = "企业名称不能为空")
    private String name;
    
    /** 社会统一信用代码 */
    @ApiModelProperty(value = "社会统一信用代码", example = "91110000123456789X", required = true)
    @NotNull(message = "社会统一信用代码不能为空")
    private String creditCode;
    
    /** 机会锁定人 */
    @ApiModelProperty(value = "机会锁定人", example = "张三", required = true)
    @NotNull(message = "机会锁定人不能为空")
    private String lockUser;
    
    /** 机会锁定时间 */
    @ApiModelProperty(value = "机会锁定时间", example = "2025-01-15 10:30:00")
    private Date lockTime;

    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间", example = "2025-01-15 11:30:00")
    private Date lockEndTime;
}
