package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 企业机会锁定更新DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定更新DTO")
public class EnterpriseOppLockUpdateDTO {
    
    /** 主键 */
    @ApiModelProperty(value = "主键", example = "1", required = true)
    @NotNull(message = "主键不能为空")
    private Integer id;
    
    /** 机会id */
    @ApiModelProperty(value = "机会ID", example = "1001")
    private Integer opportunityId;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称", example = "某某有限公司")
    private String name;
    
    /** 社会统一信用代码 */
    @ApiModelProperty(value = "社会统一信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 机会锁定人 */
    @ApiModelProperty(value = "机会锁定人", example = "张三")
    private String lockUser;
    
    /** 机会锁定时间 */
    @ApiModelProperty(value = "机会锁定时间", example = "2025-01-15 10:30:00")
    private Date lockTime;

    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间", example = "2025-01-15 11:30:00")
    private Date lockEndTime;

    /** 当前最新有效锁定记录ID */
    @ApiModelProperty(value = "当前最新有效锁定记录ID", example = "60f1c2b8a1234567890abcde")
    private String currentRecordId;
}
