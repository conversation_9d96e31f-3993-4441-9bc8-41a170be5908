package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定记录新增DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定记录新增DTO")
public class EnterpriseOppLockRecordAddDTO {
    
    /** 企业机会锁定主表ID */
    @ApiModelProperty(value = "企业机会锁定主表ID", example = "1001", required = true)
    @NotNull(message = "企业机会锁定主表ID不能为空")
    private Integer lockId;
    
    /** 企业统一社会信用代码 */
    @ApiModelProperty(value = "企业统一社会信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 锁定人 */
    @ApiModelProperty(value = "锁定人", example = "张三", required = true)
    @NotNull(message = "锁定人不能为空")
    private String lockUser;
    
    /** 锁定时间 */
    @ApiModelProperty(value = "锁定时间", example = "2025-01-15 10:30:00")
    private Date lockTime;
    
    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间", example = "2025-01-16 10:30:00")
    private Date lockEndTime;
    
    /** 锁定文件列表（最多10个文件） */
    @ApiModelProperty(value = "锁定文件列表")
    private List<LockFileInfo> lockFiles;
    
    /**
     * 锁定文件信息内部类
     */
    @Data
    @ApiModel(description = "锁定文件信息")
    public static class LockFileInfo {
        
        /** 文件名称 */
        @ApiModelProperty(value = "文件名称", example = "合同.pdf", required = true)
        @NotNull(message = "文件名称不能为空")
        private String fileName;
        
        /** 文件地址 */
        @ApiModelProperty(value = "文件地址", example = "http://xxx.com/file.pdf", required = true)
        @NotNull(message = "文件地址不能为空")
        private String fileUrl;
        
        /** 文件大小（字节） */
        @ApiModelProperty(value = "文件大小", example = "1024000")
        private Long fileSize;
        
        /** 文件类型 */
        @ApiModelProperty(value = "文件类型", example = "pdf")
        private String fileType;
        
        /** 上传时间 */
        @ApiModelProperty(value = "上传时间", example = "2025-01-15 10:30:00")
        private Date uploadTime;
    }
}
