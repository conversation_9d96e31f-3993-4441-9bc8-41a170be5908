package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 机会锁定操作DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "机会锁定操作DTO")
public class OpportunityLockDTO {
    
    /** 机会ID */
    @ApiModelProperty(value = "机会ID", example = "1001", required = true)
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;
    
    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间", example = "2025-01-15 18:00:00")
    private Date lockEndTime;
    
    /** 锁定文件记录 */
    @ApiModelProperty(value = "锁定文件记录（最多10条）")
    private List<LockFileInfo> lockFiles;
    
    /**
     * 锁定文件信息内部类
     */
    @Data
    @ApiModel(description = "锁定文件信息")
    public static class LockFileInfo {
        
        /** 文件名称 */
        @ApiModelProperty(value = "文件名称", example = "合同.pdf", required = true)
        private String fileName;
        
        /** 文件地址 */
        @ApiModelProperty(value = "文件地址", example = "http://example.com/files/contract.pdf", required = true)
        private String fileUrl;
        
        /** 文件大小（字节） */
        @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
        private Long fileSize;
        
        /** 文件类型 */
        @ApiModelProperty(value = "文件类型", example = "application/pdf")
        private String fileType;
        
        /** 上传时间 */
        @ApiModelProperty(value = "上传时间", example = "2025-01-15 10:30:00")
        private Date uploadTime;
    }
}
