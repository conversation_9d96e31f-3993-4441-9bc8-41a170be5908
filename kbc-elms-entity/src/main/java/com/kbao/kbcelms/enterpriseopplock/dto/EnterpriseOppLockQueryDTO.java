package com.kbao.kbcelms.enterpriseopplock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业机会锁定查询DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定查询DTO")
public class EnterpriseOppLockQueryDTO {
    
    /** 机会id */
    @ApiModelProperty(value = "机会ID", example = "1001")
    private Integer opportunityId;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称", example = "某某有限公司")
    private String name;
    
    /** 社会统一信用代码 */
    @ApiModelProperty(value = "社会统一信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 机会锁定人 */
    @ApiModelProperty(value = "机会锁定人", example = "张三")
    private String lockUser;

    /** 锁定时间开始 */
    @ApiModelProperty(value = "锁定时间开始", example = "2025-01-01 00:00:00")
    private Date lockTimeStart;

    /** 锁定时间结束 */
    @ApiModelProperty(value = "锁定时间结束", example = "2025-01-31 23:59:59")
    private Date lockTimeEnd;

    /** 锁定截止时间开始 */
    @ApiModelProperty(value = "锁定截止时间开始", example = "2025-01-01 00:00:00")
    private Date lockEndTimeStart;

    /** 锁定截止时间结束 */
    @ApiModelProperty(value = "锁定截止时间结束", example = "2025-01-31 23:59:59")
    private Date lockEndTimeEnd;
    
    /** 创建时间开始 */
    @ApiModelProperty(value = "创建时间开始", example = "2025-01-01 00:00:00")
    private Date createTimeStart;
    
    /** 创建时间结束 */
    @ApiModelProperty(value = "创建时间结束", example = "2025-01-31 23:59:59")
    private Date createTimeEnd;
}
