package com.kbao.kbcelms.enterpriseopplock.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企业机会锁定记录VO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "企业机会锁定记录VO")
public class EnterpriseOppLockRecordVO {
    
    /** 主键 */
    @ApiModelProperty(value = "主键", example = "507f1f77bcf86cd799439011")
    private String id;
    
    /** 企业机会锁定主表ID */
    @ApiModelProperty(value = "企业机会锁定主表ID", example = "1001")
    private Integer lockId;
    
    /** 企业统一社会信用代码 */
    @ApiModelProperty(value = "企业统一社会信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 机会类型 */
    @ApiModelProperty(value = "机会类型: 1-员服，2-综合", example = "1")
    private String opportunityType;
    
    /** 锁定人 */
    @ApiModelProperty(value = "锁定人", example = "张三")
    private String lockUser;
    
    /** 锁定时间 */
    @ApiModelProperty(value = "锁定时间", example = "2025-01-15 10:30:00")
    private Date lockTime;
    
    /** 锁定截止时间 */
    @ApiModelProperty(value = "锁定截止时间", example = "2025-01-16 10:30:00")
    private Date lockEndTime;
    
    /** 锁定文件列表 */
    @ApiModelProperty(value = "锁定文件列表")
    private List<LockFileInfo> lockFiles;
    
    /** 记录状态 */
    @ApiModelProperty(value = "记录状态: 0-无效，1-有效", example = "1")
    private Integer status;
    
    /** 创建人 */
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createId;
    
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "2025-01-15 10:30:00")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateId;
    
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-01-15 10:30:00")
    private Date updateTime;
    
    /**
     * 锁定文件信息内部类
     */
    @Data
    @ApiModel(description = "锁定文件信息")
    public static class LockFileInfo {
        
        /** 文件名称 */
        @ApiModelProperty(value = "文件名称", example = "合同.pdf")
        private String fileName;
        
        /** 文件地址 */
        @ApiModelProperty(value = "文件地址", example = "http://xxx.com/file.pdf")
        private String fileUrl;
        
        /** 文件大小（字节） */
        @ApiModelProperty(value = "文件大小", example = "1024000")
        private Long fileSize;
        
        /** 文件类型 */
        @ApiModelProperty(value = "文件类型", example = "pdf")
        private String fileType;
        
        /** 上传时间 */
        @ApiModelProperty(value = "上传时间", example = "2025-01-15 10:30:00")
        private Date uploadTime;
    }
}
