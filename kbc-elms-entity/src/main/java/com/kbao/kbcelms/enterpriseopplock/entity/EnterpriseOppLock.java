package com.kbao.kbcelms.enterpriseopplock.entity;

import lombok.Data;

import java.util.Date;

/**
 * 企业机会锁定表
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class EnterpriseOppLock {
    
    /** 主键 */
    private Integer id;
    
    /** 机会id */
    private Integer opportunityId;
    
    /** 机会类型: 1-员服，2-综合 */
    private String opportunityType;
    
    /** 企业名称 */
    private String name;
    
    /** 社会统一信用代码 */
    private String creditCode;
    
    /** 机会锁定人 */
    private String lockUser;
    
    /** 机会锁定时间 */
    private Date lockTime;

    /** 锁定截止时间 */
    private Date lockEndTime;

    /** 当前最新有效锁定记录ID */
    private String currentRecordId;

    /** 创建人 */
    private String createId;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateId;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 是否删除 */
    private Integer isDeleted;
}
