<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunity.dao.OpportunityMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunity.entity.Opportunity">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="bizCode" column="biz_code" jdbcType="VARCHAR" />
        <result property="agentCode" column="agent_code" jdbcType="VARCHAR" />
        <result property="agentName" column="agent_name" jdbcType="VARCHAR" />
        <result property="opportunityName" column="opportunity_name" jdbcType="VARCHAR" />
        <result property="agentEnterpriseId" column="agent_enterprise_id" jdbcType="INTEGER" />
        <result property="opportunityType" column="opportunity_type" jdbcType="CHAR" />
        <result property="industryCode" column="industry_code" jdbcType="VARCHAR" />
        <result property="status" column="status" jdbcType="INTEGER" />
        <result property="closeReasonType" column="close_reason_type" jdbcType="INTEGER" />
        <result property="processStep" column="process_step" jdbcType="VARCHAR" />
        <result property="areaCenterCode" column="area_center_code" jdbcType="VARCHAR" />
        <result property="areaCenterName" column="area_center_name" jdbcType="VARCHAR" />
        <result property="legalCode" column="legal_code" jdbcType="VARCHAR" />
        <result property="legalName" column="legal_name" jdbcType="VARCHAR" />
        <result property="companyCode" column="company_code" jdbcType="VARCHAR" />
        <result property="companyName" column="company_name" jdbcType="VARCHAR" />
        <result property="tradingCenterCode" column="trading_center_code" jdbcType="VARCHAR" />
        <result property="tradingCenterName" column="trading_center_name" jdbcType="VARCHAR" />
        <result property="salesCenterCode" column="sales_center_code" jdbcType="VARCHAR" />
        <result property="salesCenterName" column="sales_center_name" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="currentProcessId" column="current_process_id" jdbcType="INTEGER" />
        <result property="coordinator" column="coordinator" jdbcType="VARCHAR" />
        <result property="projectManager" column="project_manager" jdbcType="VARCHAR" />
        <result property="projectOrgCode" column="project_org_code" jdbcType="VARCHAR" />
        <result property="projectOrgName" column="project_org_name" jdbcType="VARCHAR" />
        <result property="lockStatus" column="lock_status" jdbcType="INTEGER" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List">
        id, biz_code, agent_code, agent_name, opportunity_name, agent_enterprise_id, opportunity_type, industry_code, status, close_reason_type, process_step, area_center_code, area_center_name, legal_code, legal_name, company_code, company_name, trading_center_code, trading_center_name, sales_center_code, sales_center_name, create_id, create_time, update_id, update_time, tenant_id, current_process_id, coordinator, project_manager, project_org_code, project_org_name, lock_status, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.biz_code,
        t.agent_code,
        t.agent_name,
        t.opportunity_name,
        t.agent_enterprise_id,
        t.opportunity_type,
        t.industry_code,
        t.status,
        t.close_reason_type,
        t.process_step,
        t.area_center_code,
        t.area_center_name,
        t.legal_code,
        t.legal_name,
        t.company_code,
        t.company_name,
        t.trading_center_code,
        t.trading_center_name,
        t.sales_center_code,
        t.sales_center_name,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.tenant_id,
        t.current_process_id,
        t.coordinator,
        t.project_manager,
        t.project_org_code,
        t.project_org_name,
        t.lock_status,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="bizCode != null">
                and t.biz_code = #{bizCode,jdbcType=VARCHAR}
            </if>
            <if test="agentCode != null">
                and t.agent_code = #{agentCode,jdbcType=VARCHAR}
            </if>
            <if test="agentName != null">
                and t.agent_name = #{agentName,jdbcType=VARCHAR}
            </if>
            <if test="opportunityName != null">
                and t.opportunity_name = #{opportunityName,jdbcType=VARCHAR}
            </if>
            <if test="agentEnterpriseId != null">
                and t.agent_enterprise_id = #{agentEnterpriseId,jdbcType=INTEGER}
            </if>
            <if test="opportunityType != null">
                and t.opportunity_type = #{opportunityType,jdbcType=CHAR}
            </if>
            <if test="industryCode != null">
                and t.industry_code = #{industryCode,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and t.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="closeReasonType != null">
                and t.close_reason_type = #{closeReasonType,jdbcType=INTEGER}
            </if>
            <if test="processStep != null">
                and t.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            <if test="areaCenterCode != null">
                and t.area_center_code = #{areaCenterCode,jdbcType=VARCHAR}
            </if>
            <if test="areaCenterName != null">
                and t.area_center_name = #{areaCenterName,jdbcType=VARCHAR}
            </if>
            <if test="legalCode != null">
                and t.legal_code = #{legalCode,jdbcType=VARCHAR}
            </if>
            <if test="legalName != null">
                and t.legal_name = #{legalName,jdbcType=VARCHAR}
            </if>
            <if test="companyCode != null">
                and t.company_code = #{companyCode,jdbcType=VARCHAR}
            </if>
            <if test="companyName != null">
                and t.company_name = #{companyName,jdbcType=VARCHAR}
            </if>
            <if test="tradingCenterCode != null">
                and t.trading_center_code = #{tradingCenterCode,jdbcType=VARCHAR}
            </if>
            <if test="tradingCenterName != null">
                and t.trading_center_name = #{tradingCenterName,jdbcType=VARCHAR}
            </if>
            <if test="salesCenterCode != null">
                and t.sales_center_code = #{salesCenterCode,jdbcType=VARCHAR}
            </if>
            <if test="salesCenterName != null">
                and t.sales_center_name = #{salesCenterName,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="currentProcessId != null">
                and t.current_process_id = #{currentProcessId,jdbcType=INTEGER}
            </if>
            <if test="coordinator != null">
                and t.coordinator = #{coordinator,jdbcType=VARCHAR}
            </if>
            <if test="projectManager != null">
                and t.project_manager = #{projectManager,jdbcType=VARCHAR}
            </if>
            <if test="projectOrgCode != null">
                and t.project_org_code = #{projectOrgCode,jdbcType=VARCHAR}
            </if>
            <if test="projectOrgName != null">
                and t.project_org_name = #{projectOrgName,jdbcType=VARCHAR}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunity.entity.Opportunity" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity (
            biz_code, agent_code, agent_name, opportunity_name, agent_enterprise_id, opportunity_type, industry_code, status,
            close_reason_type, process_step, area_center_code, area_center_name, legal_code, legal_name, company_code,
            company_name, trading_center_code, trading_center_name, sales_center_code, sales_center_name,
            create_id, create_time, update_id, update_time, tenant_id, current_process_id, coordinator, project_manager,
            project_org_code, project_org_name, lock_status, is_deleted
        ) values (
            #{bizCode}, #{agentCode}, #{agentName}, #{opportunityName}, #{agentEnterpriseId}, #{opportunityType}, #{industryCode}, #{status},
            #{closeReasonType}, #{processStep}, #{areaCenterCode}, #{areaCenterName}, #{legalCode}, #{legalName}, #{companyCode},
            #{companyName}, #{tradingCenterCode}, #{tradingCenterName}, #{salesCenterCode}, #{salesCenterName},
            #{createId}, now(), #{createId}, now(), #{tenantId}, #{currentProcessId}, #{coordinator}, #{projectManager},
            #{projectOrgCode}, #{projectOrgName}, #{lockStatus}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunity.entity.Opportunity" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizCode != null">biz_code,</if>
            <if test="agentCode != null">agent_code,</if>
            <if test="agentName != null">agent_name,</if>
            <if test="opportunityName != null">opportunity_name,</if>
            <if test="agentEnterpriseId != null">agent_enterprise_id,</if>
            <if test="opportunityType != null">opportunity_type,</if>
            <if test="industryCode != null">industry_code,</if>
            <if test="status != null">status,</if>
            <if test="closeReasonType != null">close_reason_type,</if>
            <if test="processStep != null">process_step,</if>
            <if test="areaCenterCode != null">area_center_code,</if>
            <if test="areaCenterName != null">area_center_name,</if>
            <if test="legalCode != null">legal_code,</if>
            <if test="legalName != null">legal_name,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyName != null">company_name,</if>
            <if test="tradingCenterCode != null">trading_center_code,</if>
            <if test="tradingCenterName != null">trading_center_name,</if>
            <if test="salesCenterCode != null">sales_center_code,</if>
            <if test="salesCenterName != null">sales_center_name,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="currentProcessId != null">current_process_id,</if>
            <if test="coordinator != null">coordinator,</if>
            <if test="projectManager != null">project_manager,</if>
            <if test="projectOrgCode != null">project_org_code,</if>
            <if test="projectOrgName != null">project_org_name,</if>
            <if test="lockStatus != null">lock_status,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizCode != null">#{bizCode},</if>
            <if test="agentCode != null">#{agentCode},</if>
            <if test="agentName != null">#{agentName},</if>
            <if test="opportunityName != null">#{opportunityName},</if>
            <if test="agentEnterpriseId != null">#{agentEnterpriseId},</if>
            <if test="opportunityType != null">#{opportunityType},</if>
            <if test="industryCode != null">#{industryCode},</if>
            <if test="status != null">#{status},</if>
            <if test="closeReasonType != null">#{closeReasonType},</if>
            <if test="processStep != null">#{processStep},</if>
            <if test="areaCenterCode != null">#{areaCenterCode},</if>
            <if test="areaCenterName != null">#{areaCenterName},</if>
            <if test="legalCode != null">#{legalCode},</if>
            <if test="legalName != null">#{legalName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="tradingCenterCode != null">#{tradingCenterCode},</if>
            <if test="tradingCenterName != null">#{tradingCenterName},</if>
            <if test="salesCenterCode != null">#{salesCenterCode},</if>
            <if test="salesCenterName != null">#{salesCenterName},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="currentProcessId != null">#{currentProcessId},</if>
            <if test="coordinator != null">#{coordinator},</if>
            <if test="projectManager != null">#{projectManager},</if>
            <if test="projectOrgCode != null">#{projectOrgCode},</if>
            <if test="projectOrgName != null">#{projectOrgName},</if>
            <if test="lockStatus != null">#{lockStatus},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunity.entity.Opportunity">
        update t_opportunity set
            biz_code = #{bizCode},
            agent_code = #{agentCode},
            agent_name = #{agentName},
            opportunity_name = #{opportunityName},
            agent_enterprise_id = #{agentEnterpriseId},
            opportunity_type = #{opportunityType},
            industry_code = #{industryCode},
            status = #{status},
            close_reason_type = #{closeReasonType},
            process_step = #{processStep},
            area_center_code = #{areaCenterCode},
            area_center_name = #{areaCenterName},
            legal_code = #{legalCode},
            legal_name = #{legalName},
            company_code = #{companyCode},
            company_name = #{companyName},
            trading_center_code = #{tradingCenterCode},
            trading_center_name = #{tradingCenterName},
            sales_center_code = #{salesCenterCode},
            sales_center_name = #{salesCenterName},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            tenant_id = #{tenantId},
            current_process_id = #{currentProcessId},
            coordinator = #{coordinator},
            project_manager = #{projectManager},
            project_org_code = #{projectOrgCode},
            project_org_name = #{projectOrgName},
            lock_status = #{lockStatus},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunity.entity.Opportunity">
        update t_opportunity
        <set>
            <if test="bizCode != null">biz_code = #{bizCode},</if>
            <if test="agentCode != null">agent_code = #{agentCode},</if>
            <if test="agentName != null">agent_name = #{agentName},</if>
            <if test="opportunityName != null">opportunity_name = #{opportunityName},</if>
            <if test="agentEnterpriseId != null">agent_enterprise_id = #{agentEnterpriseId},</if>
            <if test="opportunityType != null">opportunity_type = #{opportunityType},</if>
            <if test="industryCode != null">industry_code = #{industryCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="closeReasonType != null">close_reason_type = #{closeReasonType},</if>
            <if test="processStep != null">process_step = #{processStep},</if>
            <if test="areaCenterCode != null">area_center_code = #{areaCenterCode},</if>
            <if test="areaCenterName != null">area_center_name = #{areaCenterName},</if>
            <if test="legalCode != null">legal_code = #{legalCode},</if>
            <if test="legalName != null">legal_name = #{legalName},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="tradingCenterCode != null">trading_center_code = #{tradingCenterCode},</if>
            <if test="tradingCenterName != null">trading_center_name = #{tradingCenterName},</if>
            <if test="salesCenterCode != null">sales_center_code = #{salesCenterCode},</if>
            <if test="salesCenterName != null">sales_center_name = #{salesCenterName},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="currentProcessId != null">current_process_id = #{currentProcessId},</if>
            <if test="coordinator != null">coordinator = #{coordinator},</if>
            <if test="projectManager != null">project_manager = #{projectManager},</if>
            <if test="projectOrgCode != null">project_org_code = #{projectOrgCode},</if>
            <if test="projectOrgName != null">project_org_name = #{projectOrgName},</if>
            <if test="lockStatus != null">lock_status = #{lockStatus},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity set is_deleted = 1, update_time = now() where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity (
            biz_code, agent_code, agent_name, opportunity_name, agent_enterprise_id, opportunity_type, industry_code, status, close_reason_type, process_step, area_center_code, area_center_name, legal_code, legal_name, company_code, company_name, trading_center_code, trading_center_name, sales_center_code, sales_center_name, create_id, create_time, update_id, update_time, tenant_id, current_process_id, coordinator, project_manager, project_org_code, project_org_name, lock_status, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.bizCode}, #{item.agentCode}, #{item.agentName}, #{item.opportunityName}, #{item.agentEnterpriseId}, #{item.opportunityType}, #{item.industryCode}, #{item.status}, #{item.closeReasonType}, #{item.processStep}, #{item.areaCenterCode}, #{item.areaCenterName}, #{item.legalCode}, #{item.legalName}, #{item.companyCode}, #{item.companyName}, #{item.tradingCenterCode}, #{item.tradingCenterName}, #{item.salesCenterCode}, #{item.salesCenterName}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, #{item.tenantId}, #{item.currentProcessId}, #{item.coordinator}, #{item.projectManager}, #{item.projectOrgCode}, #{item.projectOrgName}, #{item.lockStatus}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByProcessInstanceIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select o.*
        from t_opportunity o
        inner join t_opportunity_process op on o.id = op.opportunity_id
        where op.bpm_process_id in
        <foreach collection="processInstanceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and o.is_deleted = 0
        and op.is_deleted = 0
    </select>

    <!-- 新增：OpportunityTodoVO结果映射 -->
    <resultMap id="OpportunityTodoResultMap" type="com.kbao.kbcelms.opportunity.vo.OpportunityTodoVO">
        <result column="id" property="opportunityId"/>
        <result column="opportunity_name" property="opportunityName"/>
        <result column="biz_code" property="bizCode"/>
        <result column="agent_name" property="agentName"/>
        <result column="company_name" property="companyName"/>
        <result column="sales_center_name" property="salesCenterName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="credit_code" property="creditCode"/>
        <result column="general_insurance_type" property="generalInsuranceType"/>
        <result column="is_bid" property="isBid"/>
        <result column="submit_time" property="submitTime"/>
        <result column="status" property="status"/>
        <result column="lock_status" property="lockStatus"/>
        <result column="insure_num" property="insureNum"/>
        <result column="premium_budget" property="premiumBudget"/>
        <result column="process_step" property="processStep"/>
        <result column="opportunity_type" property="opportunityType"/>
        <result column="close_reason_type" property="closeReasonType"/>
        <result column="participation_status" property="participationStatus"/>
        <result column="division_id" property="divisionId"/>
        <result column="division_ratio" property="divisionRatio"/>
    </resultMap>

    <!-- 新增：OpportunityDetailVO结果映射 -->
    <resultMap id="OpportunityDetailResultMap" type="com.kbao.kbcelms.opportunity.vo.OpportunityDetailVO">
        <result column="id" property="id"/>
        <result column="biz_code" property="bizCode"/>
        <result column="opportunity_name" property="opportunityName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="agent_name" property="agentName"/>
        <result column="trading_center_name" property="tradingCenterName"/>
        <result column="sales_center_name" property="salesCenterName"/>
        <result column="opportunity_type" property="opportunityType"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="process_step" property="processStep"/>
        <result column="lock_status" property="lockStatus"/>
        <result column="general_insurance_type" property="generalInsuranceType"/>
        <result column="insure_num" property="insureNum"/>
        <result column="premium_budget" property="premiumBudget"/>
        <result column="is_bid" property="isBid"/>
        <result column="enterpriseName" property="enterpriseName"/>
        <result column="enterpriseId" property="enterpriseId"/>
        <result column="company_code" property="companyCode"/>
        <result column="company_name" property="companyName"/>
        <result column="industry_code" property="industryCode"/>
        <result column="credit_code" property="creditCode"/>
        <result column="dt_type" property="dtType"/>
        <result column="submit_time" property="submitTime"/>
        <result column="team_time" property="teamTime"/>
        <result column="log_time" property="logTime"/>
        <result column="summary_time" property="summaryTime"/>
        <result column="projectManagerName" property="projectManagerName"/>
        <result column="add_health_service" property="addHealthService"/>
        <result column="health_service_code" property="healthServiceCode"/>
        <result column="health_service_name" property="healthServiceName"/>
        <result column="add_rescue_service" property="addRescueService"/>
        <result column="rescue_service_code" property="rescueServiceCode"/>
        <result column="rescue_service_name" property="rescueServiceName"/>
        <result column="kyc_report_url" property="kycReportUrl"/>
        <result column="risk_report_url" property="riskReportUrl"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="kyc_report_time" property="kycReportTime"/>
    </resultMap>

    <!-- 新增：OpportunityExportVO结果映射 -->
    <resultMap id="OpportunityExportResultMap" type="com.kbao.kbcelms.opportunity.vo.OpportunityExportVO">
        <result column="id" property="id"/>
        <result column="opportunity_name" property="opportunityName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="agent_name" property="agentName"/>
        <result column="company_code" property="companyCode"/>
        <result column="company_name" property="companyName"/>
        <result column="trading_center_name" property="tradingCenterName"/>
        <result column="sales_center_name" property="salesCenterName"/>
        <result column="opportunity_type" property="opportunityType"/>
        <result column="industry_code" property="industryCode"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="lock_status" property="lockStatus"/>
        <result column="biz_code" property="bizCode"/>
        <result column="area_center_code" property="areaCenterCode"/>
        <result column="area_center_name" property="areaCenterName"/>
        <result column="legal_code" property="legalCode"/>
        <result column="legal_name" property="legalName"/>
        <result column="process_step" property="processStep"/>
        <result column="coordinator" property="coordinator"/>
        <result column="project_manager" property="projectManager"/>
        <result column="project_org_code" property="projectOrgCode"/>
        <result column="project_org_name" property="projectOrgName"/>
        <result column="general_insurance_name" property="generalInsuranceName"/>
        <result column="insure_num" property="insureNum"/>
        <result column="premium_budget" property="premiumBudget"/>
        <result column="is_bid" property="isBid"/>
        <result column="submit_time" property="submitTime"/>
        <result column="team_time" property="teamTime"/>
        <result column="log_time" property="logTime"/>
        <result column="contacter" property="contacter"/>
        <result column="contacter_post" property="contacterPost"/>
        <result column="add_health_service" property="addHealthService"/>
        <result column="health_service_code" property="healthServiceCode"/>
        <result column="health_service_name" property="healthServiceName"/>
        <result column="add_rescue_service" property="addRescueService"/>
        <result column="rescue_service_code" property="rescueServiceCode"/>
        <result column="rescue_service_name" property="rescueServiceName"/>
        <result column="employee_insurance_type" property="employeeInsuranceType"/>
        <result column="general_insurance_type" property="generalInsuranceType"/>
        <result column="remark" property="remark"/>
        <result column="has_history_policy" property="hasHistoryPolicyText"/>
        <result column="policy_expire_time" property="policyExpireTime"/>
        <result column="bid_result" property="bidResult"/>
        <result column="bid_start_date" property="bidStartDate"/>
        <result column="bid_end_date" property="bidEndDate"/>
        <result column="enterpriseId" property="enterpriseId"/>
        <result column="enterpriseName" property="enterpriseName"/>
        <result column="credit_code" property="creditCode"/>
        <result column="dt_type" property="dtTypeText"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="enterprise_scale" property="enterpriseScale"/>
        <result column="city" property="city"/>
        <result column="staff_scale" property="staffScale"/>
        <result column="annual_income" property="annualIncome"/>
        <result column="is_verified" property="isVerified"/>
        <result column="enterprise_contacter" property="enterpriseContacter"/>
        <result column="contacter_phone" property="contacterPhone"/>
        <result column="enterprise_remark" property="enterpriseRemark"/>
        <result column="projectManagerName" property="projectManagerName"/>
        <result column="projectManagerPhone" property="projectManagerPhone"/>
        <result column="projectManagerEmail" property="projectManagerEmail"/>
        <result column="coordinatorName" property="coordinatorName"/>
        <result column="coordinatorPhone" property="coordinatorPhone"/>
        <result column="coordinatorEmail" property="coordinatorEmail"/>
        <result column="kyc_report_time" property="kycReportTime"/>
        <result column="risk_report_time" property="riskReportTime"/>
        <result column="coordination_accept_time" property="coordinationAcceptTime"/>
        <result column="assign_coordination_time" property="assignCoordinationTime"/>
        <result column="assign_project_manager_time" property="assignProjectManagerTime"/>
        <result column="project_team_info" property="projectTeamInfo"/>
        <result column="close_time" property="closeTime"/>
        <result column="summary_time" property="summaryTime"/>
        <result column="ranking_time" property="rankingTime"/>
        <result column="policy_time" property="policyTime"/>
        <result column="suspend_time" property="suspendTime"/>
        <result column="restart_time" property="restartTime"/>
        <result column="process_step" property="processStep"/>
    </resultMap>

    <!-- 新增：多表关联查询，返回OpportunityDetailVO数组 -->
    <select id="selectOpportunityDetails" resultMap="OpportunityDetailResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityDetailQuery">
        select
        unity.id,
        unity.biz_code,
        unity.opportunity_name,
        unity.agent_code,
        unity.agent_name,
        unity.company_name,
        unity.trading_center_name,
        unity.sales_center_name,
        unity.opportunity_type,
        unity.create_time,
        unity.status,
        unity.lock_status,
        unity.process_step,
        unity.company_code,
        unity.industry_code,
        unity.process_step,
        detail.general_insurance_type,
        detail.insure_num,
        detail.premium_budget,
        detail.is_bid,
        detail.submit_time,
        detail.team_time,
        detail.log_time,
        detail.summary_time,
        detail.add_health_service,
        detail.health_service_code,
        detail.health_service_name,
        detail.add_rescue_service,
        detail.rescue_service_code,
        detail.rescue_service_name,
        detail.kyc_report_time,
        detail.kyc_report_url,
        detail.risk_report_url,
        enterprise.id as enterpriseId,
        enterprise.name as enterpriseName,
        enterprise.credit_code as credit_code,
        enterprise.dt_type as dt_type,
        enterprise.is_verified,
        enterprise.verify_time,
        u.nick_name as projectManagerName
        from t_opportunity unity
        <!-- 关联机会扩展详情 -->
        left join t_opportunity_detail detail on unity.id = detail.opportunity_id
        <!-- 关联用户 -->
        left join t_user u on unity.project_manager = u.user_id
        <!-- 关联企业 -->
        left join t_gen_agent_enterprise enterprise on unity.agent_enterprise_id = enterprise.id
        where
        unity.tenant_id = #{tenantId}  <!-- 必选参数 -->
        and unity.is_deleted = 0

        <!-- 所属机构模糊查询 -->
        <if test="legalCode != null and legalCode != ''">
            and unity.legal_code like concat('%', #{legalCode}, '%')
        </if>
        <!-- 可选参数：机会名称模糊查询 -->
        <if test="opportunityName != null and opportunityName != ''">
            and unity.opportunity_name like concat('%', #{opportunityName}, '%')
        </if>
        <!-- 可选参数：机会ID -->
        <if test="opportunityId != null and opportunityId !=''">
            and unity.id = #{opportunityId}
        </if>
        <if test="bizCode != null and bizCode !=''">
            and unity.biz_code = #{bizCode}
        </if>
        <!-- 可选参数：状态列表 -->
        <if test="status != null">
            and unity.status in
            <foreach collection="status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="opportunityStatus != null">
            and unity.status = #{opportunityStatus}
        </if>
        <if test="processStep != null  and processStep!=''">
            and unity.process_step = #{processStep}
        </if>

        <!-- 可选参数：用户昵称模糊查询 -->
        <if test="name != null and name != ''">
            and u.nick_name like concat('%', #{name}, '%')
        </if>
        <!-- 可选参数 ：agentName模糊匹配 -->
        <if test="agentName != null and agentName != ''">
            and unity.agent_name like concat('%', #{agentName}, '%')
        </if>
        <!-- 可选参数 ：companyCode精确匹配 -->
        <if test="companyCode != null and companyCode != ''">
            and unity.company_code = #{companyCode}
        </if>
        <!-- 可选参数 ：opportunityName模糊匹配 -->
        <if test="opportunityName != null and opportunityName != ''">
            and unity.opportunity_name like concat('%', #{opportunityName}, '%')
        </if>
        <!-- 可选参数 ：industryCode精确匹配 -->
        <if test="industryCode != null and industryCode != ''">
            and unity.industry_code = #{industryCode}
        </if>
        <!-- 可选参数 ：opportunityType精确匹配 -->
        <if test="opportunityType != null">
            and unity.opportunity_type = #{opportunityType}
        </if>
        <!-- 可选参数 ：opportunityIds批量匹配 -->
        <if test="opportunityIds != null and opportunityIds.size() > 0">
            and unity.id in
            <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!-- 可选参数 ：enterpriseName模糊匹配 -->
        <if test="enterpriseName != null and enterpriseName != ''">
            and enterprise.name like concat('%', #{enterpriseName}, '%')
        </if>
        <!-- 可选参数 ：creditCode精确匹配 -->
        <if test="creditCode != null and creditCode != ''">
            and enterprise.credit_code = #{creditCode}
        </if>
        <!-- 可选参数 ：dtType精确匹配 -->
        <if test="dtType != null and dtType!=''">
            and enterprise.dt_type = #{dtType}
        </if>
        <!-- 可选参数 ：submitTime时间范围 -->
        <if test="submitBeginTime != null">
            and detail.submit_time &gt;= #{submitBeginTime}
        </if>
        <if test="submitEndTime != null">
            and detail.submit_time &lt;= #{submitEndTime}
        </if>
        <!-- 可选参数 ：teamTime时间范围 -->
        <if test="teamBeginTime != null">
            and detail.team_time &gt;= #{teamBeginTime}
        </if>
        <if test="teamEndTime != null">
            and detail.team_time &lt;= #{teamEndTime}
        </if>
        <!-- 可选参数 ：logTime时间范围 -->
        <if test="logBeginTime != null">
            and detail.log_time &gt;= #{logBeginTime}
        </if>
        <if test="logEndTime != null">
            and detail.log_time &lt;= #{logEndTime}
        </if>
        <!-- 可选参数 ：summaryTime时间范围 -->
        <if test="summaryBeginTime != null">
            and detail.summary_time &gt;= #{summaryBeginTime}
        </if>
        <if test="summaryEndTime != null">
            and detail.summary_time &lt;= #{summaryEndTime}
        </if>
        <!-- 可选参数 ：isVerified精确匹配 -->
        <if test="isVerified != null and isVerified != ''">
            and enterprise.is_verified = #{isVerified}
        </if>
        order by detail.submit_time desc
    </select>

    <!-- 新增：通过opportunityIds列表查询机会 -->
    <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select <include refid="Base_Column_List" />
        from t_opportunity
        where id in
        <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 员工线索统计查询 -->
    <select id="statAgentEnterprise" resultType="com.kbao.kbcelms.opportunity.vo.EmployeeLeadVO" parameterType="com.kbao.kbcelms.opportunity.entity.Opportunity">
        select
            ae.agent_code agentCode,
            ae.agent_name agentName,
            ae.legal_name legalName,
            ae.trading_center_name tradingCenterName,
            count(distinct ae.id) agentEnterpriseNum,
            count(distinct if(ae.is_verified = '1', ae.id, null)) verifiedEnterpriseNum,
            count(distinct op.id) opportunityNum,
            sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,
            sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum
        from t_gen_agent_enterprise ae
            left join t_opportunity op on op.agent_enterprise_id = ae.id and op.is_deleted = 0
        <where>
            ae.is_deleted = 0
            <if test="tenantId != null and tenantId != ''">
                and ae.tenant_id = #{tenantId}
            </if>
            <if test="agentName != null and agentName != ''">
                and ae.agent_name like concat('%', #{agentName}, '%')
            </if>
            <if test="agentCode != null and agentCode != ''">
                and ae.agent_code like concat('%', #{agentCode}, '%')
            </if>
        </where>
        group by ae.agent_code
        order by ae.agent_code
    </select>

    <!-- 根据流程实例ID集合查询待办任务详细信息 -->
    <select id="selectTodoTasksByProcessInstanceIds" resultMap="OpportunityTodoResultMap" parameterType="java.util.List">
        select
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        inner join t_opportunity_process op on o.id = op.opportunity_id
        where op.bpm_process_id in
        <foreach collection="processInstanceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <![CDATA[ and o.status <> 0 ]]>
        and o.is_deleted = 0
        and op.is_deleted = 0
    </select>

    <!-- 根据机会ID集合查询待参与任务详细信息 -->
    <select id="selectPendingParticipationTasks" resultMap="OpportunityTodoResultMap" parameterType="java.util.List">
        select
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        where o.is_deleted = 0
        <![CDATA[ and o.status <> 0 ]]>
        <if test="opportunityIds != null and opportunityIds.size() > 0">
            and o.id in
            <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by od.submit_time desc
    </select>

    <!-- 分页查询我参与的机会详细信息（合并团队参与和流程日志参与） -->
    <select id="selectParticipatedOpportunitiesWithPagination" resultMap="OpportunityTodoResultMap" parameterType="java.util.Map">
        select distinct
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
                 left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
                 left join t_opportunity_detail od on o.id = od.opportunity_id
        where o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
        and o.tenant_id = #{tenantId,jdbcType=VARCHAR}
          and (
            -- 在项目团队中确定参与（join_type = 1）
            exists (
                select 1 from t_opportunity_team ot
                where ot.opportunity_id = o.id
                  and ot.user_id = #{userId,jdbcType=VARCHAR}
                  and ot.tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and ot.join_type = 1
                  and ot.is_deleted = 0
            )
                or
                -- 在机会流程日志中存在过记录
            exists (
                select 1 from t_opportunity_process_log opl
                where opl.opportunity_id = o.id
                  and (opl.operator_id = #{userId,jdbcType=VARCHAR} or opl.target_id = #{userId,jdbcType=VARCHAR})
                  and opl.tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and opl.is_deleted = 0
            )
            )
        order by od.submit_time desc
    </select>

    <!-- 分页查询待办任务详细信息（支持多种查询条件） -->
    <select id="selectTodoTasksWithPagination" resultMap="OpportunityTodoResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO">
        select
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        inner join t_opportunity_process op on o.id = op.opportunity_id
        <where>
            op.is_deleted = 0
            and o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
            
            <!-- 流程实例ID条件 -->
            <if test="processInstanceIds != null and processInstanceIds.size() > 0">
                and op.bpm_process_id in
                <foreach collection="processInstanceIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            
            <!-- 顾问姓名模糊查询 -->
            <if test="agentName != null and agentName != ''">
                and o.agent_name like concat('%', #{agentName}, '%')
            </if>
            
            <!-- 所属机构模糊查询 -->
            <if test="legalCode != null and legalCode != ''">
                and o.legal_code like concat('%', #{legalCode}, '%')
            </if>
            
            <!-- 机会名称模糊查询 -->
            <if test="opportunityName != null and opportunityName != ''">
                and o.opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            
            <!-- 机会ID精确查询 -->
            <if test="opportunityId != null">
                and o.id = #{opportunityId}
            </if>
            
            <!-- 机会提交时间范围查询 -->
            <if test="submitTimeStart != null">
                and od.submit_time >= #{submitTimeStart}
            </if>
            <if test="submitTimeEnd != null">
                and od.submit_time &lt;= #{submitTimeEnd}
            </if>
            
            <!-- 企业名称模糊查询 -->
            <if test="enterpriseName != null and enterpriseName != ''">
                and e.name like concat('%', #{enterpriseName}, '%')
            </if>
            
            <!-- 企业代码模糊查询 -->
            <if test="creditCode != null and creditCode != ''">
                and e.credit_code like concat('%', #{creditCode}, '%')
            </if>
            
            <!-- 机会状态精确查询 -->
            <if test="status != null">
                and o.status = #{status}
            </if>
            
            <!-- 机会类型精确查询 -->
            <if test="opportunityType != null and opportunityType != ''">
                and o.opportunity_type = #{opportunityType}
            </if>
            
            <!-- 机会关闭原因类型精确查询 -->
            <if test="closeReasonType != null">
                and o.close_reason_type = #{closeReasonType}
            </if>

            <!-- 步骤           -->
            <if test="processStep != null and processStep !=''">
                and o.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            
            <!-- 计划编码精确查询 -->
            <if test="bizCode != null and bizCode != ''">
                and o.biz_code = #{bizCode}
            </if>
        </where>
        order by od.submit_time desc
    </select>

    <!-- 统计待办任务总数（支持多种查询条件） -->
    <select id="countTodoTasks" resultType="java.lang.Long" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO">
        select count(distinct o.id)
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        inner join t_opportunity_process op on o.id = op.opportunity_id
        <where>
            op.is_deleted = 0
            and o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
            
            <!-- 流程实例ID条件 -->
            <if test="processInstanceIds != null and processInstanceIds.size() > 0">
                and op.bpm_process_id in
                <foreach collection="processInstanceIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            
            <!-- 顾问姓名模糊查询 -->
            <if test="agentName != null and agentName != ''">
                and o.agent_name like concat('%', #{agentName}, '%')
            </if>
            
            <!-- 所属机构模糊查询 -->
            <if test="legalCode != null and legalCode != ''">
                and o.legal_code like concat('%', #{legalCode}, '%')
            </if>
            
            <!-- 机会名称模糊查询 -->
            <if test="opportunityName != null and opportunityName != ''">
                and o.opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            
            <!-- 机会ID精确查询 -->
            <if test="opportunityId != null">
                and o.id = #{opportunityId}
            </if>
            
            <!-- 机会提交时间范围查询 -->
            <if test="submitTimeStart != null">
                and od.submit_time >= #{submitTimeStart}
            </if>
            <if test="submitTimeEnd != null">
                and od.submit_time &lt;= #{submitTimeEnd}
            </if>
            
            <!-- 企业名称模糊查询 -->
            <if test="enterpriseName != null and enterpriseName != ''">
                and e.name like concat('%', #{enterpriseName}, '%')
            </if>
            
            <!-- 企业代码模糊查询 -->
            <if test="creditCode != null and creditCode != ''">
                and e.credit_code like concat('%', #{creditCode}, '%')
            </if>
            
            <!-- 机会状态精确查询 -->
            <if test="status != null">
                and o.status = #{status}
            </if>

            <!-- 步骤           -->
            <if test="processStep != null and processStep !=''">
                and o.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            
            <!-- 计划编码精确查询 -->
            <if test="bizCode != null and bizCode != ''">
                and o.biz_code = #{bizCode}
            </if>
        </where>
    </select>

    <!-- 查询机会详情列表（用于导出，补充缺失字段） -->
    <select id="selectOpportunityDetailsForExport" resultMap="OpportunityExportResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityDetailQuery">
        select
        unity.id,
        unity.opportunity_name,
        unity.agent_code,
        unity.agent_name,
        unity.company_name,
        unity.trading_center_name,
        unity.sales_center_name,
        unity.opportunity_type,
        unity.create_time,
        unity.status,
        unity.lock_status,
        unity.company_code,
        unity.industry_code,
        detail.general_insurance_type,
        detail.insure_num,
        detail.premium_budget,
        detail.is_bid,
        detail.submit_time,
        detail.team_time,
        detail.log_time,
        detail.summary_time,
        detail.add_health_service,
        detail.health_service_code,
        detail.health_service_name,
        detail.add_rescue_service,
        detail.rescue_service_code,
        detail.rescue_service_name,
        detail.kyc_report_time,
        detail.kyc_report_url,
        detail.risk_report_url,
        enterprise.id as enterpriseId,
        enterprise.name as enterpriseName,
        enterprise.credit_code as credit_code,
        enterprise.dt_type as dt_type,
        enterprise.is_verified,
        enterprise.verify_time,
        u.nick_name as projectManagerName
        from t_opportunity unity
        <!-- 关联机会扩展详情 -->
        left join t_opportunity_detail detail on unity.id = detail.opportunity_id
        <!-- 关联用户 -->
        left join t_user u on unity.project_manager = u.user_id
        <!-- 关联企业 -->
        left join t_gen_agent_enterprise enterprise on unity.agent_enterprise_id = enterprise.id
        where
        unity.tenant_id = #{tenantId}  <!-- 必选参数 -->
        and unity.is_deleted = 0

        <!-- 所属机构模糊查询 -->
        <if test="legalCode != null and legalCode != ''">
            and unity.legal_code like concat('%', #{legalCode}, '%')
        </if>
        <!-- 可选参数：机会名称模糊查询 -->
        <if test="opportunityName != null and opportunityName != ''">
            and unity.opportunity_name like concat('%', #{opportunityName}, '%')
        </if>
        <!-- 可选参数：机会ID -->
        <if test="opportunityId != null and opportunityId !=''">
            and unity.id = #{opportunityId}
        </if>
        <!-- 可选参数：状态列表 -->
        <if test="status != null">
            and unity.status in
            <foreach collection="status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="opportunityStatus != null">
            and unity.status = #{opportunityStatus}
        </if>
        <if test="processStep != null  and processStep!=''">
            and unity.process_step = #{processStep}
        </if>

        <!-- 可选参数：用户昵称模糊查询 -->
        <if test="name != null and name != ''">
            and u.nick_name like concat('%', #{name}, '%')
        </if>
        <!-- 可选参数 ：agentName模糊匹配 -->
        <if test="agentName != null and agentName != ''">
            and unity.agent_name like concat('%', #{agentName}, '%')
        </if>
        <!-- 可选参数 ：companyCode精确匹配 -->
        <if test="companyCode != null and companyCode != ''">
            and unity.company_code = #{companyCode}
        </if>
        <!-- 可选参数 ：opportunityName模糊匹配 -->
        <if test="opportunityName != null and opportunityName != ''">
            and unity.opportunity_name like concat('%', #{opportunityName}, '%')
        </if>
        <!-- 可选参数 ：industryCode精确匹配 -->
        <if test="industryCode != null and industryCode != ''">
            and unity.industry_code = #{industryCode}
        </if>
        <!-- 可选参数 ：opportunityType精确匹配 -->
        <if test="opportunityType != null">
            and unity.opportunity_type = #{opportunityType}
        </if>
        <!-- 可选参数 ：opportunityIds批量匹配 -->
        <if test="opportunityIds != null and opportunityIds.size() > 0">
            and unity.id in
            <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!-- 可选参数 ：enterpriseName模糊匹配 -->
        <if test="enterpriseName != null and enterpriseName != ''">
            and enterprise.name like concat('%', #{enterpriseName}, '%')
        </if>
        <!-- 可选参数 ：creditCode精确匹配 -->
        <if test="creditCode != null and creditCode != ''">
            and enterprise.credit_code = #{creditCode}
        </if>
        <!-- 可选参数 ：dtType精确匹配 -->
        <if test="dtType != null">
            and enterprise.dt_type = #{dtType}
        </if>
        <!-- 可选参数 ：submitTime时间范围 -->
        <if test="submitBeginTime != null">
            and detail.submit_time &gt;= #{submitBeginTime}
        </if>
        <if test="submitEndTime != null">
            and detail.submit_time &lt;= #{submitEndTime}
        </if>
        <!-- 可选参数 ：teamTime时间范围 -->
        <if test="teamBeginTime != null">
            and detail.team_time &gt;= #{teamBeginTime}
        </if>
        <if test="teamEndTime != null">
            and detail.team_time &lt;= #{teamEndTime}
        </if>
        <!-- 可选参数 ：logTime时间范围 -->
        <if test="logBeginTime != null">
            and detail.log_time &gt;= #{logBeginTime}
        </if>
        <if test="logEndTime != null">
            and detail.log_time &lt;= #{logEndTime}
        </if>
        <!-- 可选参数 ：summaryTime时间范围 -->
        <if test="summaryBeginTime != null">
            and detail.summary_time &gt;= #{summaryBeginTime}
        </if>
        <if test="summaryEndTime != null">
            and detail.summary_time &lt;= #{summaryEndTime}
        </if>
        <!-- 可选参数 ：isVerified精确匹配 -->
        <if test="isVerified != null and isVerified != ''">
            and enterprise.is_verified = #{isVerified}
        </if>
        order by unity.create_time desc
    </select>

    <!-- 分页查询待参与任务详细信息（支持多种查询条件） -->
    <select id="selectPendingParticipationTasksWithPagination" resultMap="OpportunityTodoResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO">
        select distinct
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step,
            otd.status as participation_status
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        left join t_opportunity_team ot on o.id = ot.opportunity_id and ot.is_deleted = 0 and ot.user_id = #{userId} and ot.tenant_id = #{tenantId} and ot.join_type = 0
        left join t_opportunity_team_division otd on o.id = otd.opportunity_id and otd.user_id = #{userId} and otd.status = 0 and otd.is_deleted = 0
        where o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
            and (
                (ot.opportunity_id is not null)
                or 
                (otd.opportunity_id is not null)
            )
            
            <!-- 顾问姓名模糊查询 -->
            <if test="agentName != null and agentName != ''">
                and o.agent_name like concat('%', #{agentName}, '%')
            </if>
            
            <!-- 所属机构模糊查询 -->
            <if test="legalCode != null and legalCode != ''">
                and o.legal_code like concat('%', #{legalCode}, '%')
            </if>
            
            <!-- 机会名称模糊查询 -->
            <if test="opportunityName != null and opportunityName != ''">
                and o.opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            
            <!-- 机会ID精确查询 -->
            <if test="opportunityId != null">
                and o.id = #{opportunityId}
            </if>
            
            <!-- 机会提交时间范围查询 -->
            <if test="submitTimeStart != null">
                and od.submit_time >= #{submitTimeStart}
            </if>
            <if test="submitTimeEnd != null">
                and od.submit_time &lt;= #{submitTimeEnd}
            </if>
            
            <!-- 企业名称模糊查询 -->
            <if test="enterpriseName != null and enterpriseName != ''">
                and e.name like concat('%', #{enterpriseName}, '%')
            </if>
            
            <!-- 企业代码模糊查询 -->
            <if test="creditCode != null and creditCode != ''">
                and e.credit_code like concat('%', #{creditCode}, '%')
            </if>
            
            <!-- 机会状态精确查询 -->
            <if test="status != null">
                and o.status = #{status}
            </if>
            
            <!-- 机会类型精确查询 -->
            <if test="opportunityType != null and opportunityType != ''">
                and o.opportunity_type = #{opportunityType}
            </if>
            
            <!-- 机会关闭原因类型精确查询 -->
            <if test="closeReasonType != null">
                and o.close_reason_type = #{closeReasonType}
            </if>

            <!-- 步骤           -->
            <if test="processStep != null and processStep !=''">
                and o.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            
            <!-- 计划编码精确查询 -->
            <if test="bizCode != null and bizCode != ''">
                and o.biz_code = #{bizCode}
            </if>
        order by od.submit_time desc
    </select>



    <!-- 分页查询我参与的机会详细信息（支持多种查询条件） -->
    <select id="selectParticipatedOpportunitiesWithPaginationAndConditions" resultMap="OpportunityTodoResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO">
        select distinct
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        where o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
            and o.tenant_id = #{tenantId}
            and (
                -- 在项目团队中确定参与（join_type = 1）
                exists (
                    select 1 from t_opportunity_team ot 
                    where ot.opportunity_id = o.id 
                    and ot.user_id = #{userId}
                    and ot.tenant_id = #{tenantId}
                    and ot.join_type = 1
                    and ot.is_deleted = 0
                )
                or
                -- 在机会流程日志中存在过记录
                exists (
                    select 1 from t_opportunity_process_log opl
                    where opl.opportunity_id = o.id
                    and (opl.operator_id = #{userId} or opl.target_id = #{userId})
                    and opl.tenant_id = #{tenantId}
                    and opl.is_deleted = 0
                )
            )
            
            <!-- 顾问姓名模糊查询 -->
            <if test="agentName != null and agentName != ''">
                and o.agent_name like concat('%', #{agentName}, '%')
            </if>
            
            <!-- 所属机构模糊查询 -->
            <if test="legalCode != null and legalCode != ''">
                and o.legal_code like concat('%', #{legalCode}, '%')
            </if>
            
            <!-- 机会名称模糊查询 -->
            <if test="opportunityName != null and opportunityName != ''">
                and o.opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            
            <!-- 机会ID精确查询 -->
            <if test="opportunityId != null">
                and o.id = #{opportunityId}
            </if>
            
            <!-- 机会提交时间范围查询 -->
            <if test="submitTimeStart != null">
                and od.submit_time >= #{submitTimeStart}
            </if>
            <if test="submitTimeEnd != null">
                and od.submit_time &lt;= #{submitTimeEnd}
            </if>
            
            <!-- 企业名称模糊查询 -->
            <if test="enterpriseName != null and enterpriseName != ''">
                and e.name like concat('%', #{enterpriseName}, '%')
            </if>
            
            <!-- 企业代码模糊查询 -->
            <if test="creditCode != null and creditCode != ''">
                and e.credit_code like concat('%', #{creditCode}, '%')
            </if>
            
            <!-- 机会状态精确查询 -->
            <if test="status != null">
                and o.status = #{status}
            </if>
            
            <!-- 机会类型精确查询 -->
            <if test="opportunityType != null and opportunityType != ''">
                and o.opportunity_type = #{opportunityType}
            </if>
            
            <!-- 机会关闭原因类型精确查询 -->
            <if test="closeReasonType != null">
                and o.close_reason_type = #{closeReasonType}
            </if>

            <!-- 步骤           -->
            <if test="processStep != null and processStep !=''">
                and o.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            
            <!-- 计划编码精确查询 -->
            <if test="bizCode != null and bizCode != ''">
                and o.biz_code = #{bizCode}
            </if>
        order by od.submit_time desc
    </select>

    <!-- 分页查询所有已提交的机会（支持多种查询条件） -->
    <select id="selectAllOpportunitiesWithPagination" resultMap="OpportunityTodoResultMap" parameterType="com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO">
        select
            o.id,
            o.opportunity_name,
            o.biz_code,
            o.agent_name,
            o.company_name,
            o.sales_center_name,
            o.status,
            o.lock_status,
            o.opportunity_type,
            o.close_reason_type,
            e.name as enterprise_name,
            e.credit_code,
            od.general_insurance_type,
            od.is_bid,
            od.submit_time,
            od.insure_num,
            od.premium_budget,
            o.process_step
        from t_opportunity o
        left join t_gen_agent_enterprise e on o.agent_enterprise_id = e.id
        left join t_opportunity_detail od on o.id = od.opportunity_id
        <where>
            o.is_deleted = 0
            <![CDATA[ and o.status <> 0 ]]>
            and o.tenant_id = #{tenantId}
            
            <!-- 顾问姓名模糊查询 -->
            <if test="agentName != null and agentName != ''">
                and o.agent_name like concat('%', #{agentName}, '%')
            </if>
            
            <!-- 所属机构模糊查询 -->
            <if test="legalCode != null and legalCode != ''">
                and o.legal_code like concat('%', #{legalCode}, '%')
            </if>
            
            <!-- 机会名称模糊查询 -->
            <if test="opportunityName != null and opportunityName != ''">
                and o.opportunity_name like concat('%', #{opportunityName}, '%')
            </if>
            
            <!-- 机会ID精确查询 -->
            <if test="opportunityId != null">
                and o.id = #{opportunityId}
            </if>
            
            <!-- 机会提交时间范围查询 -->
            <if test="submitTimeStart != null">
                <![CDATA[
                and od.submit_time >= #{submitTimeStart}
                ]]>
            </if>
            <if test="submitTimeEnd != null">
                <![CDATA[
                and od.submit_time <= #{submitTimeEnd}
                ]]>
            </if>
            
            <!-- 企业名称模糊查询 -->
            <if test="enterpriseName != null and enterpriseName != ''">
                and e.name like concat('%', #{enterpriseName}, '%')
            </if>
            
            <!-- 企业代码模糊查询 -->
            <if test="creditCode != null and creditCode != ''">
                and e.credit_code like concat('%', #{creditCode}, '%')
            </if>
            
            <!-- 机会状态精确查询 -->
            <if test="status != null">
                and o.status = #{status}
            </if>
            
            <!-- 机会类型精确查询 -->
            <if test="opportunityType != null and opportunityType != ''">
                and o.opportunity_type = #{opportunityType}
            </if>
            
            <!-- 机会关闭原因类型精确查询 -->
            <if test="closeReasonType != null">
                and o.close_reason_type = #{closeReasonType}
            </if>
            <!-- 步骤           -->
            <if test="processStep != null and processStep !=''">
                and o.process_step = #{processStep,jdbcType=VARCHAR}
            </if>
            
            <!-- 计划编码精确查询 -->
            <if test="bizCode != null and bizCode != ''">
                and o.biz_code = #{bizCode}
            </if>
        </where>
        order by od.submit_time desc
    </select>

    <select id="getAgentOpportunityList" resultType="com.kbao.kbcelms.opportunity.vo.OpportunityListResVo">
        select op.id opportunityId, op.opportunity_name opportunityName, op.biz_code bizCode,
        op.opportunity_type opportunityType, op.status,
        if(op.opportunity_type = '1', od.employee_insurance_type, od.general_insurance_type) insureTypes,
        od.submit_time submitTime, od.has_insure_info hasInsureInfo, ae.name,
        if((select count(1) from t_opportunity_team_division where opportunity_id = op.id
            and user_id = #{agentCode} and is_deleted = 0 and status = 0 and times > 0) > 0, '1', '0') isNeedConfirm
        from t_opportunity op
        left join t_opportunity_detail od on op.id = od.opportunity_id
        left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id
        where op.agent_code = #{agentCode} and op.is_deleted = 0
        <if test="keyword != null and keyword != ''">
            and instr(op.opportunity_name, #{keyword}) > 0
        </if>
        <if test="enterpriseId != null and enterpriseId != ''">
            and op.agent_enterprise_id = #{enterpriseId}
        </if>
        order by isNeedConfirm desc, op.create_time desc
    </select>

    <select id="getEmployeeInsureTypes" resultType="string">
        select od.employee_insurance_type from t_opportunity o
        left join t_opportunity_detail od on o.id = od.opportunity_id
        where o.opportunity_type = '1' and o.is_deleted = 0
        and o.id = #{opportunityId}
    </select>

    <update id="submitOpportunity">
        update t_opportunity set status = '1', update_time = now()
        where id = #{opportunityId}
    </update>

    <select id="getSubmitOpportunityNum" resultType="int">
        select count(1) from t_opportunity where status > 0 and is_deleted = 0
        and agent_enterprise_id = #{enterpriseId}
    </select>

    <select id="getAgentOpportunityNum" resultType="int">
        select count(1) from t_opportunity where is_deleted = 0
        and agent_enterprise_id = #{agentEnterpriseId}
    </select>

    <select id="getOpportunityNum" resultType="com.kbao.kbcelms.enterpriseconfirmation.bean.AgentEnterpriseOtherDto">
        select agent_enterprise_id agentEnterpriseId,  count(1) opportunityNum,
               group_concat(if(o.opportunity_type = '1' and o.status > 0, od.employee_insurance_type, ''), ',') underwayEmployeeInsureTypes,
               group_concat(if(o.opportunity_type = '2' and o.status > 0, od.general_insurance_type, ''), ',') underwayGeneralInsureTypes,
               group_concat(if(o.opportunity_type = '1' and o.status = 2, od.employee_insurance_type, ''), ',') lockEmployeeInsureTypes,
               group_concat(if(o.opportunity_type = '2' and o.status = 2, od.general_insurance_type, ''), ',') lockGeneralInsureTypes,
               group_concat(if(o.opportunity_type = '1' and t.opportunity_id is not null, od.employee_insurance_type, ''), ',') issuedEmployeeInsureTypes,
               group_concat(if(o.opportunity_type = '2' and t.opportunity_id is not null, od.general_insurance_type, ''), ',') issuedGeneralInsureTypes
        from t_opportunity o left join t_opportunity_detail od on o.id = od.opportunity_id
        left join (select opportunity_id from t_opportunity_order group by opportunity_id) t on t.opportunity_id = o.id
        where o.is_deleted = 0
        and agent_enterprise_id in
        <foreach collection="agentEnterpriseIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by o.agent_enterprise_id
    </select>

    <!-- 根据机会ID数组批量查询对应的BPM流程ID -->
    <select id="selectBpmProcessIdsByOpportunityIds" resultType="java.lang.String">
        select 
            op.bpm_process_id
        from t_opportunity o
        left join t_opportunity_process op on o.current_process_id = op.id
        where o.is_deleted = 0
        and op.is_deleted = 0
        and o.id in
        <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>