<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.user.dao.UserMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.user.entity.User">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="userId" column="user_id" jdbcType="VARCHAR" />
        <result property="bscUseName" column="bsc_use_name" jdbcType="VARCHAR" />
        <result property="nickName" column="nick_name" jdbcType="VARCHAR" />
        <result property="phone" column="phone" jdbcType="VARCHAR" />
        <result property="email" column="email" jdbcType="VARCHAR" />
        <result property="ehrUserCode" column="ehr_user_code" jdbcType="VARCHAR" />
        <result property="agentCode" column="agent_code" jdbcType="VARCHAR" />
        <result property="personDesc" column="person_desc" jdbcType="VARCHAR" />
        <result property="insuranceTypes" column="insurance_types" jdbcType="VARCHAR" />
        <result property="insuranceNames" column="insurance_names" jdbcType="VARCHAR" />
        <result property="industryTypes" column="industry_types" jdbcType="VARCHAR" />
        <result property="industryNames" column="industry_names" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, bsc_use_name, nick_name, phone, email, ehr_user_code, agent_code, person_desc, insurance_types, insurance_names, industry_types, industry_names, create_id, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.user_id,
        t.bsc_use_name,
        t.nick_name,
        t.phone,
        t.email,
        t.ehr_user_code,
        t.agent_code,
        t.person_desc,
        t.insurance_types,
        t.insurance_names,
        t.industry_types,
        t.industry_names,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="userId != null">
                and t.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="bscUseName != null">
                and t.bsc_use_name = #{bscUseName,jdbcType=VARCHAR}
            </if>
            <if test="nickName != null">
                and t.nick_name = #{nickName,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and t.phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="email != null">
                and t.email = #{email,jdbcType=VARCHAR}
            </if>
            <if test="ehrUserCode != null">
                and t.ehr_user_code = #{ehrUserCode,jdbcType=VARCHAR}
            </if>
            <if test="agenCode != null">
                and t.agent_code = #{agentCode,jdbcType=VARCHAR}
            </if>
            <if test="personDesc != null">
                and t.person_desc = #{personDesc,jdbcType=VARCHAR}
            </if>
            <if test="insuranceTypes != null">
                and t.insurance_types = #{insuranceTypes,jdbcType=VARCHAR}
            </if>
            <if test="insuranceNames != null">
                and t.insurance_names = #{insuranceNames,jdbcType=VARCHAR}
            </if>
            <if test="industryTypes != null">
                and t.industry_types = #{industryTypes,jdbcType=VARCHAR}
            </if>
            <if test="industryNames != null">
                and t.industry_names = #{industryNames,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_user where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_user t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.user.entity.User" useGeneratedKeys="true" keyProperty="id">
        insert into t_user (
            user_id, bsc_use_name, nick_name, phone, email, ehr_user_code, agent_code, person_desc, insurance_types,insurance_names, industry_types,industry_names, create_id, create_time, update_id, update_time, is_deleted
        ) values (
            #{userId}, #{bscUseName}, #{nickName}, #{phone}, #{email}, #{ehrUserCode}, #{agentCode}, #{personDesc}, #{insuranceTypes}, #{insuranceNames}, #{industryTypes}, #{industryNames}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.user.entity.User" useGeneratedKeys="true" keyProperty="id">
        insert into t_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bscUseName != null">bsc_use_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="ehrUserCode != null">ehr_user_code,</if>
            <if test="agentCode != null">agent_code,</if>
            <if test="personDesc != null">person_desc,</if>
            <if test="insuranceTypes != null">insurance_types,</if>
            <if test="insuranceNames != null">insurance_names,</if>
            <if test="industryTypes != null">industry_types,</if>
            <if test="industryNames != null">industry_names,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bscUseName != null">#{bscUseName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="ehrUserCode != null">#{ehrUserCode},</if>
            <if test="agentCode != null">#{agentCode},</if>
            <if test="personDesc != null">#{personDesc},</if>
            <if test="insuranceTypes != null">#{insuranceTypes},</if>
            <if test="insuranceNames != null">#{insuranceNames},</if>
            <if test="industryTypes != null">#{industryTypes},</if>
            <if test="industryNames != null">#{industryNames},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.user.entity.User">
        update t_user set
            user_id = #{userId},
            bsc_use_name = #{bscUseName},
            nick_name = #{nickName},
            phone = #{phone},
            email = #{email},
            ehr_user_code = #{ehrUserCode},
            agent_code = #{agentCode},
            person_desc = #{personDesc},
            insurance_types = #{insuranceTypes},
            insurance_names = #{insuranceNames},
            industry_types = #{industryTypes},
            industry_names = #{industryNames},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.user.entity.User">
        update t_user
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bscUseName != null">bsc_use_name = #{bscUseName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="ehrUserCode != null">ehr_user_code = #{ehrUserCode},</if>
            <if test="agentCode != null">agent_code = #{agentCode},</if>
            <if test="personDesc != null">person_desc = #{personDesc},</if>
            <if test="insuranceTypes != null">insurance_types = #{insuranceTypes},</if>
            <if test="insuranceNames != null">insurance_names = #{insuranceNames},</if>
            <if test="industryTypes != null">industry_types = #{industryTypes},</if>
            <if test="industryNames != null">industry_names = #{industryNames},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_user set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_user (
            user_id, bsc_use_name, nick_name, phone, email, ehr_user_code, agent_code, person_desc, insurance_types, insurance_names, industry_types, industry_names, create_id, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.bscUseName}, #{item.nickName}, #{item.phone}, #{item.email}, #{item.ehrUserCode}, #{agentCode},#{item.personDesc}, #{item.insuranceTypes}, #{item.insuranceNames}, #{item.industryTypes}, #{item.industryNames}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_user set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="detail" parameterType="java.util.HashMap" resultType="com.kbao.kbcelms.user.vo.UserResponseVO">
        select
            t.id ,
            t.user_id as userId,
            t.email ,
            t.phone ,
            t.agent_code as agentCode ,
            t.bsc_use_name as bscUserName ,
            t.nick_name as nickName ,
            t.ehr_user_code as ehrUserCode ,
            t.industry_types as industryTypes ,
            t.industry_names as industryNames ,
            t.insurance_types as insuranceTypes ,
            t.insurance_names as insuranceNames ,
            t.person_desc as personDesc,
            ut.relation_type as relationType,
            ut.organ_code as organCode,
            ut.organ_name as organName,
            ut.organ_code_path as organCodePath,
            ut.organ_name_path as organNamePath,
            ut.expert_type as expertType,
            ut.wechat_account as wechatAccount
        from
            t_user t
                left join t_user_tenant ut on
                t.user_id = ut.user_id
        where
            t.id = #{id} and ut.tenant_id = #{tenantId,jdbcType=VARCHAR} and ut.is_deleted = 0
    </select>

    <select id="findByCondition" parameterType="java.util.Map" resultType="com.kbao.kbcelms.user.vo.UserResponseVO">
        select
            t.id ,
            t.user_id as userId,
            t.email ,
            t.phone ,
            t.agent_code as agentCode ,
            t.bsc_use_name as bscUserName ,
            t.nick_name as nickName ,
            t.ehr_user_code as ehrUserCode ,
            t.industry_types as industryTypes ,
            t.industry_names as industryNames ,
            t.insurance_types as insuranceTypes ,
            t.insurance_names as insuranceNames ,
            ut.relation_type as relationType,
            ut.organ_code as organCode,
            ut.organ_code_path as organCodePath,
            ut.organ_name_path  as organNamePath,
            ut.status,
            ut.id as userTenantId,
            (
            select
            group_CONCAT(role_name)
            from
            t_user_role ur
            left join t_role r on
            ur.role_id = r.id
            where
            ur.user_id = t.user_id
            and ur.is_deleted = 0
            and r.tenant_id = #{tenantId}
            group by
            t.user_id
            ) as roleName,
            (
            select
            org_type
            from
            t_user_org o
            where
            o.user_id = t.user_id
            and o.is_deleted = 0
            and o.tenant_id = #{tenantId}
            limit 1) as orgType
        from
            t_user t
            left join t_user_tenant ut on
            t.user_id = ut.user_id
        where
            t.is_deleted = 0
            and ut.is_deleted =0
            and ut.tenant_id = #{tenantId}
        <if test="nickName != null and nickName !=''">
            <![CDATA[ and t.nick_name LIKE CONCAT('%',#{nickName},'%') ]]>
        </if>
        <if test="roleName != null and roleName !=''">
            and t.user_id in (
            select ur.user_id
            from
            t_role r left join t_user_role ur on r.id = ur.role_id
            where r.is_deleted = 0 and ur.is_deleted = 0 and r.tenant_id = #{tenantId}
            <![CDATA[ and r.role_name LIKE CONCAT('%',#{roleName},'%') ]]>)
        </if>
        <if test="industryNames != null and industryNames !=''">
            <![CDATA[ and t.industry_names LIKE CONCAT('%',#{industryNames},'%') ]]>
        </if>
        <if test="insuranceNames != null and insuranceNames !=''">
            <![CDATA[ and t.insurance_names LIKE CONCAT('%',#{insuranceNames},'%') ]]>
        </if>
        <if test="email != null and email !=''">
            <![CDATA[ and t.email LIKE CONCAT('%',#{email},'%') ]]>
        </if>
        <if test="organName != null and organName !=''">
            <![CDATA[ and ut.organ_name_path LIKE CONCAT('%',#{organName},'%') ]]>
        </if>
        order by
        t.update_time desc
    </select>

    <select id="selectBranchCoordinatorUsers" resultType="java.util.HashMap">
        SELECT DISTINCT
            u.user_id as userId,
            u.nick_name as nickName,
            u.bsc_use_name as userName,
            #{roleType} as roleType,
            ut.organ_code as organCode,
            ut.organ_name as organName,
            u.phone as phone,
            u.email as email
        FROM
            t_user u
            JOIN t_user_tenant ut ON u.user_id = ut.user_id and ut.is_deleted = 0
        WHERE
            EXISTS (
                SELECT 1 
                FROM t_user_role ur 
                JOIN t_role r ON ur.role_id = r.id 
                WHERE ur.user_id = u.user_id 
                    AND ur.is_deleted = 0 
                    AND r.is_deleted = 0 
                    AND r.tenant_id = ut.tenant_id
                    AND r.role_type = #{roleType}
            )
            <if test="organCode != null and organCode != ''">
                <![CDATA[ AND ut.organ_code_path LIKE CONCAT('%', #{organCode}, '%') ]]>
            </if>
            <if test="nickName != null and nickName != ''">
                <![CDATA[ AND u.nick_name LIKE CONCAT('%', #{nickName}, '%') ]]>
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND ut.tenant_id = #{tenantId}
            </if>
            AND u.is_deleted = 0
    </select>

    <select id="getByBscUserNameAndTenantId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user t left join t_user_tenant ut on t.user_id = ut.user_id
        where t.bsc_use_name = #{bscUseName,jdbcType=VARCHAR} and ut.tenant_id = #{tenantId,jdbcType=VARCHAR} and
        t.is_deleted = 0 and ut.is_deleted = 0
    </select>

    <resultMap id="UserRoleAuthResultMap" type="com.kbao.kbcelms.user.vo.UserRoleAuthVO">
        <result property="userId" jdbcType="VARCHAR" column="user_id"/>
        <result property="userName" jdbcType="VARCHAR" column="nick_name"/>
        <collection property="userRoles" ofType="com.kbao.kbcelms.user.vo.UserRoleVO">
            <result property="roleId" jdbcType="INTEGER"  column="role_id" />
            <result property="roleName" jdbcType="VARCHAR"  column="role_name" />
            <result property="roleType" jdbcType="VARCHAR"  column="role_type" />
            <collection property="roleAuths" ofType="com.kbao.kbcelms.user.vo.RoleAuthVO">
                <result property="authCode" jdbcType="VARCHAR"  column="auth_code" />
                <result property="authName" jdbcType="VARCHAR"  column="auth_name" />
            </collection>
        </collection>
    </resultMap>

    <select id="getUserRoleAuthByUserId" resultMap="UserRoleAuthResultMap" parameterType="java.lang.String">
        select
            t.user_id ,
            t.nick_name ,
            ur.role_id,
            r.role_name ,
            r.role_type ,
            ra.auth_code,
            a.auth_name
        from
            t_user t
                left join t_user_role ur on
                t.user_id = ur.user_id
                left join t_role r on
                ur.role_id = r.id
                left join t_role_auth ra on
                ra.role_id = r.id
                left join t_auth a on
                ra.auth_code = a.auth_code
        where
            t.user_id = #{userId,jdbcType=VARCHAR}
          and r.tenant_id = #{tenantId,jdbcType=VARCHAR}
          and r.is_deleted = 0
          and ra.is_deleted = 0
          and a.is_deleted = 0
    </select>

    <select id="findByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Alias_Column_List"/>
        from t_user t
        where t.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="findByBscUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Alias_Column_List"/>
        from t_user t
        where t.bsc_use_name = #{bscUseName,jdbcType=VARCHAR}
          and t.is_deleted = 0
    </select>

    <update id="deleteByUserId" parameterType="com.kbao.kbcelms.user.entity.User">
        update t_user set update_id = #{updateId}, update_time = #{updateTime}, is_deleted = 1
        where user_id = #{userId} and is_deleted = 0
    </update>

    <select id="getByUserIds" resultMap="BaseResultMap">
        select * from t_user
        where user_id in (
        <foreach item="id" collection="list" separator=",">
            #{id}
        </foreach>
        )
    </select>
</mapper>