package com.kbao.kbcelms.enterprise.base.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.dao.EnterpriseBasicInfoDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import com.kbao.kbcelms.ufs.KbcUfsService;
import com.kbao.kbcelms.util.PdfUtils;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.commons.exception.BusinessException;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 企业基本信息业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
public class EnterpriseBasicInfoService extends BaseMongoServiceImpl<EnterpriseBasicInfo, String, EnterpriseBasicInfoDao> {

    @Autowired
    private KbcUfsService kbcUfsService;

    @Autowired
    private EnterpriseShareholderService enterpriseShareholderService;

    @Autowired
    private EnterpriseBeneficiaryService enterpriseBeneficiaryService;

    private static final String APP_CODE = "elmsWeb";

    /**
     * 分页查询企业基本信息列表
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseBasicInfo> page(PageRequest<EnterpriseBasicInfo> page) {
        EnterpriseBasicInfo queryParam = page.getParam();
        String tenantId = ElmsContext.getTenantId();

        Criteria criteria = new Criteria();
        // 添加租户条件
        criteria.and("tenantId").is(tenantId);

        // 添加查询条件
        if (queryParam != null) {
            if (StringUtils.hasText(queryParam.getName())) {
                criteria.and("name").regex(queryParam.getName(), "i"); // 忽略大小写的模糊查询
            }
            if (StringUtils.hasText(queryParam.getCreditCode())) {
                criteria.and("creditCode").is(queryParam.getCreditCode());
            }
            if (StringUtils.hasText(queryParam.getRegStatus())) {
                criteria.and("regStatus").is(queryParam.getRegStatus());
            }
            if (StringUtils.hasText(queryParam.getIndustry())) {
                criteria.and("industry").regex(queryParam.getIndustry(), "i"); // 忽略大小写的模糊查询
            }
        }

        Pagination<EnterpriseBasicInfo> pagination = new Pagination<>(page.getPageNum(), page.getPageSize(), "updateTime desc");
        return super.page(new Query(criteria), pagination);
    }

    /**
     * 根据统一社会信用代码查询企业基本信息
     * @param creditCode 统一社会信用代码
     * @return 企业基本信息
     */
    public EnterpriseBasicInfo findByCreditCode(String creditCode) {
        EnterpriseBasicInfo basicInfo = dao.findByCreditCode(creditCode);
        if (basicInfo != null) {
            // 设置格式化后的经营期限
            basicInfo.setFormattedBusinessTerm(formatBusinessTerm(basicInfo.getFromTime(), basicInfo.getToTime()));
        }
        return basicInfo;
    }

    /**
     * 根据企业名称查询企业基本信息
     * @return 企业基本信息
     */
    public List<EnterpriseBasicInfo> searchByName(String name) {
        return dao.searchByName(name);
    }

    public EnterpriseBasicInfo queryByFullName(String name) {
        return dao.queryByFullName(name);
    }

    /**
     * 保存或更新企业基本信息
     * @param basicInfo 企业基本信息
     * @return 保存后的企业基本信息
     */
    public EnterpriseBasicInfo saveOrUpdate(EnterpriseBasicInfo basicInfo) {
        Date now = new Date();

        // 设置租户ID
        if (basicInfo.getTenantId() == null) {
            basicInfo.setTenantId(ElmsContext.getTenantId());
        }
        // 从staffNumRange中提取最后一个数字并设置到staffNum
        Integer staffNum = extractStaffNumFromRange(basicInfo.getStaffNumRange());
        basicInfo.setStaffNum(staffNum);
        // 获取最小行业代码
        String minCategoryCode = this.getMinCategoryCode(basicInfo.getIndustryAll());
        basicInfo.setMinCategoryCode(minCategoryCode);
        // 检查是否已存在
        EnterpriseBasicInfo existing = findByCreditCode(basicInfo.getCreditCode());
        if (existing != null) {
            // 更新现有记录
            basicInfo.setId(existing.getId());
            basicInfo.setCreateTime(existing.getCreateTime());
            basicInfo.setUpdateTime(now); // 设置更新时间
        } else {
            // 新建记录
            basicInfo.setCreateTime(now);
            basicInfo.setUpdateTime(now);
        }

        return dao.saveOrUpdate(basicInfo);
    }

    /**
     * 从staffNumRange中提取最后一个数字并设置到staffNum字段
     * 例如：≥1000人<5000人 -> 5000
     */
    private Integer extractStaffNumFromRange(String staffNumRange) {
        if (!StringUtils.hasText(staffNumRange)) {
            return null;
        }

        // 使用正则表达式找到最后一个数字
        Matcher matcher = Pattern.compile("(\\d+)(?!.*\\d)").matcher(staffNumRange);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return null;
    }

    public String getMinCategoryCode(EnterpriseBasicInfo.IndustryAll industryAll) {
        if (industryAll == null) {
            return null;
        }
        return industryAll.getCategoryCodeFourth() != null ? industryAll.getCategoryCodeFourth() :
               industryAll.getCategoryCodeThird() != null ? industryAll.getCategoryCodeThird() :
               industryAll.getCategoryCodeSecond() != null ? industryAll.getCategoryCodeSecond() :
               industryAll.getCategoryCodeFirst();
    }

    /**
     * 生成KYC PDF报告并保存URL和生成时间到basicInfo中
     *
     * @return KYC报告URL
     */
    public String generateKycPdfReport(EnterpriseBasicInfo basicInfo, List<EnterpriseShareholder> shareholderList, List<EnterpriseBeneficiary> beneficiaryList) {
        try {
            log.info("开始生成KYC PDF报告，企业信用代码：{}", basicInfo.getCreditCode());
            // 4. 构建PDF内容
            PdfUtils.PdfContent pdfContent = buildKycPdfContent(basicInfo, shareholderList, beneficiaryList);
            // 5. 生成PDF文件名
            String fileName = generateKycPdfFileName(basicInfo);
            // 6. 生成PDF MultipartFile
            MultipartFile pdfFile = PdfUtils.generateComplexPdfMultipartFile(pdfContent, fileName);
            // 7. 上传PDF到OSS
            FileUploadResponse uploadResponse = uploadKycPdfToOss(pdfFile, fileName);
            // 8. 更新企业基本信息中的KYC报告URL和生成时间
            basicInfo.setKycReportUrl(uploadResponse.getAbsolutePath());
            basicInfo.setKycReportTime(DateUtils.getCurrentDate());

            log.info("KYC PDF报告生成成功，企业信用代码：{}, 文件路径：{}", basicInfo.getCreditCode(), uploadResponse.getAbsolutePath());
            return uploadResponse.getAbsolutePath();
        } catch (Exception e) {
            log.error("生成KYC PDF报告失败，企业信用代码：{}", basicInfo.getCreditCode(), e);
            throw new BusinessException("KYC PDF报告生成失败：" + e.getMessage());
        }
    }
    /**
     * 构建KYC PDF内容
     *
     * @param basicInfo 企业基本信息
     * @param shareholderList 股东信息列表
     * @param beneficiaryList 受益人信息列表
     * @return PDF内容配置
     */
    private PdfUtils.PdfContent buildKycPdfContent(EnterpriseBasicInfo basicInfo,
                                                   List<EnterpriseShareholder> shareholderList,
                                                   List<EnterpriseBeneficiary> beneficiaryList) {
        PdfUtils.PdfContent pdfContent = new PdfUtils.PdfContent();

        // 设置PDF标题
        String title = "企业KYC信息报告";
        pdfContent.setTitle(title);

        // 构建PDF章节
        List<PdfUtils.PdfSection> sections = new ArrayList<>();

        // 1. 企业基本信息章节
        sections.add(buildBasicInfoSection(basicInfo));

        // 2. 股东和受益人信息章节
        sections.add(buildStakeholderSection(shareholderList, beneficiaryList));

        pdfContent.setSections(sections);
        return pdfContent;
    }

    /**
     * 构建企业基本信息章节
     *
     * @param basicInfo 企业基本信息
     * @return PDF章节
     */
    private PdfUtils.PdfSection buildBasicInfoSection(EnterpriseBasicInfo basicInfo) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("企业基本信息");

        StringBuilder content = new StringBuilder();
        content.append("公司名称：").append(basicInfo.getName() != null ? basicInfo.getName() : "-").append("\n");
        content.append("法定代表人：").append(basicInfo.getLegalPersonName() != null ? basicInfo.getLegalPersonName() : "-").append("\n");
        content.append("纳税人识别号：").append(basicInfo.getCreditCode() != null ? basicInfo.getCreditCode() : "-").append("\n");
        content.append("行业：").append(basicInfo.getIndustry() != null ? basicInfo.getIndustry() : "-").append("\n");
        content.append("营业期限：").append(basicInfo.getFormattedBusinessTerm() != null ? basicInfo.getFormattedBusinessTerm() : "-").append("\n");
        content.append("注册地址：").append(basicInfo.getRegLocation() != null ? basicInfo.getRegLocation() : "-").append("\n");
        content.append("经营范围：").append(basicInfo.getBusinessScope() != null ? basicInfo.getBusinessScope() : "-").append("\n");

        section.setContent(content.toString());
        return section;
    }

    /**
     * 构建股东和受益人信息章节
     *
     * @param shareholderList 股东信息列表
     * @param beneficiaryList 受益人信息列表
     * @return PDF章节
     */
    private PdfUtils.PdfSection buildStakeholderSection(List<EnterpriseShareholder> shareholderList,
                                                        List<EnterpriseBeneficiary> beneficiaryList) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("股东和受益人信息");

        StringBuilder content = new StringBuilder();

        // 股东信息
        content.append("股东名：");
        if (shareholderList != null && !shareholderList.isEmpty()) {
            List<String> shareholderNames = new ArrayList<>();
            for (EnterpriseShareholder shareholder : shareholderList) {
                if (shareholder.getName() != null) {
                    shareholderNames.add(shareholder.getName());
                }
            }
            content.append(shareholderNames.isEmpty() ? "-" : String.join("、", shareholderNames));
        } else {
            content.append("-");
        }
        content.append("\n\n");

        // 最终受益人信息
        content.append("最终受益人名称：");
        if (beneficiaryList != null && !beneficiaryList.isEmpty()) {
            List<String> beneficiaryNames = new ArrayList<>();
            for (EnterpriseBeneficiary beneficiary : beneficiaryList) {
                if (beneficiary.getName() != null) {
                    beneficiaryNames.add(beneficiary.getName());
                }
            }
            content.append(beneficiaryNames.isEmpty() ? "-" : String.join("、", beneficiaryNames));
        } else {
            content.append("-");
        }
        content.append("\n");

        section.setContent(content.toString());
        return section;
    }

    /**
     * 生成KYC PDF文件名
     *
     * @param basicInfo 企业基本信息
     * @return 文件名
     */
    private String generateKycPdfFileName(EnterpriseBasicInfo basicInfo) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = dateFormat.format(new Date());
        String enterpriseName = basicInfo.getName() != null ? basicInfo.getName() : "企业";
        return "KYC报告_" + enterpriseName + "_" + timestamp + ".pdf";
    }

    /**
     * 格式化经营期限
     *
     * @param fromTime 经营开始时间（时间戳）
     * @param toTime 经营结束时间（时间戳）
     * @return 格式化后的经营期限字符串
     */
    public String formatBusinessTerm(Long fromTime, Long toTime) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            String fromDateStr = "-";
            String toDateStr = "-";

            // 处理开始时间
            if (fromTime != null && fromTime > 0) {
                fromDateStr = dateFormat.format(new Date(fromTime));
            }

            // 处理结束时间
            if (toTime != null && toTime > 0) {
                toDateStr = dateFormat.format(new Date(toTime));
            } else if (toTime != null && toTime == 0) {
                // 如果toTime为0，表示长期有效
                toDateStr = "长期";
            }

            // 如果开始时间和结束时间都为空，返回"-"
            if ("-".equals(fromDateStr) && "-".equals(toDateStr)) {
                return "-";
            }

            return fromDateStr + " 至 " + toDateStr;

        } catch (Exception e) {
            log.error("格式化经营期限失败，fromTime: {}, toTime: {}", fromTime, toTime, e);
            return "-";
        }
    }

    /**
     * 上传KYC PDF到OSS
     *
     * @param pdfFile PDF文件（MultipartFile类型）
     * @param fileName 文件名
     * @return 上传响应
     */
    private FileUploadResponse uploadKycPdfToOss(MultipartFile pdfFile, String fileName) {
        try {
            // 生成业务编号
            String businessNo = "KYC_PDF_" + System.currentTimeMillis();

            // 构建文件路径
            String filePath = "kyc/reports/" + fileName;

            // 调用KbcUfsService上传文件
            FileUploadResponse response = kbcUfsService.upload(
                APP_CODE,                    // 应用编码
                businessNo,                  // 业务编号
                pdfFile,                     // MultipartFile对象
                null,                       // content为null，使用MultipartFile上传
                filePath,                   // 文件路径
                FileTypeEnum.FILE           // 文件类型
            );

            if (response == null) {
                throw new RuntimeException("文件上传响应为空");
            }

            log.info("KYC PDF文件上传成功，文件名：{}, 文件大小：{} bytes, 绝对路径：{}",
                    fileName, pdfFile.getSize(), response.getAbsolutePath());
            return response;

        } catch (Exception e) {
            log.error("KYC PDF文件上传失败，文件名：{}", fileName, e);
            throw new RuntimeException("KYC PDF文件上传失败：" + e.getMessage(), e);
        }
    }
}
