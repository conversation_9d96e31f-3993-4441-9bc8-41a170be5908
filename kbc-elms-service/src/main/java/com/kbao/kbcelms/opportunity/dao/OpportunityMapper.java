package com.kbao.kbcelms.opportunity.dao;

import java.util.*;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.enterpriseconfirmation.bean.AgentEnterpriseOtherDto;import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.dto.OpportunityDetailQuery;
import com.kbao.kbcelms.opportunity.vo.*;
import com.kbao.kbcelms.opportunity.dto.OpportunityTodoQueryDTO;
import org.apache.ibatis.annotations.Param;
public interface OpportunityMapper  extends BaseMapper<Opportunity, Integer>{
	/**
	 * 根据流程实例ID集合查询机会
	 */
	List<Opportunity> selectByProcessInstanceIds(@Param("processInstanceIds") List<String> processInstanceIds);
	/**
	 * 查询机会详情列表（使用参数对象）
	 */
	List<OpportunityDetailVO> selectOpportunityDetails(OpportunityDetailQuery query);
	
	/**
	 * 查询机会详情列表（用于导出，补充缺失字段）
	 */
	List<OpportunityExportVO> selectOpportunityDetailsForExport(OpportunityDetailQuery query);
	
	/**
	 * 根据机会ID集合查询机会
	 */
	List<Opportunity> selectByIds(@Param("opportunityIds") List<Integer> opportunityIds);

	/**
	 * 查询员工线索统计列表
	 */
	List<EmployeeLeadVO> statAgentEnterprise(Opportunity queryParam);

	/**
	 * 根据流程实例ID集合查询待办任务详细信息
	 */
	List<OpportunityTodoVO> selectTodoTasksByProcessInstanceIds(@Param("processInstanceIds") List<String> processInstanceIds);

	/**
	 * 根据机会ID集合查询待参与任务详细信息
	 */
	List<OpportunityTodoVO> selectPendingParticipationTasks(@Param("opportunityIds") List<Integer> opportunityIds);

	/**
	 * 分页查询我参与的机会详细信息（合并团队参与和流程日志参与）
	 * 判断依据：在项目团队中确定参与（join_type = 1），机会流程日志中存在过的人
	 */
	List<OpportunityTodoVO> selectParticipatedOpportunitiesWithPagination(@Param("userId") String userId, @Param("tenantId") String tenantId);

	/**
	 * 分页查询待办任务详细信息（支持多种查询条件）
	 */
	List<OpportunityTodoVO> selectTodoTasksWithPagination(OpportunityTodoQueryDTO queryDTO);

	/**
	 * 统计待办任务总数（支持多种查询条件）
	 */
	Long countTodoTasks(OpportunityTodoQueryDTO queryDTO);

	    /**
     * 分页查询待参与任务详细信息（支持多种查询条件）
     */
    List<OpportunityTodoVO> selectPendingParticipationTasksWithPagination(OpportunityTodoQueryDTO queryDTO);
    


	/**
	 * 分页查询我参与的机会详细信息（支持多种查询条件）
	 */
	List<OpportunityTodoVO> selectParticipatedOpportunitiesWithPaginationAndConditions(OpportunityTodoQueryDTO queryDTO);

	/**
	 * 分页查询所有已提交的机会（支持多种查询条件）
	 */
	List<OpportunityTodoVO> selectAllOpportunitiesWithPagination(OpportunityTodoQueryDTO queryDTO);

    List<OpportunityListResVo> getAgentOpportunityList(OpportunitySearchReqVo reqVo);

    String getEmployeeInsureTypes(@Param("opportunityId") Integer opportunityId);

    void submitOpportunity(@Param("opportunityId") Integer opportunityId);

    int getSubmitOpportunityNum(@Param("enterpriseId") Integer enterpriseId);

    int getAgentOpportunityNum(@Param("agentEnterpriseId") Integer agentEnterpriseId);

    List<AgentEnterpriseOtherDto> getOpportunityNum(@Param("agentEnterpriseIds") List<Integer> agentEnterpriseIds);

    /**
     * 根据机会ID数组批量查询对应的BPM流程ID
     * 通过机会表的current_process_id关联查询opportunity_process表的bmp_process_id
     * 
     * @param opportunityIds 机会ID数组
     * @return BPM流程ID列表
     */
    List<String> selectBpmProcessIdsByOpportunityIds(@Param("opportunityIds") List<Integer> opportunityIds);

    /**
     * 根据机会类型和企业ID查询进行中的机会列表
     * @param opportunityType 机会类型
     * @param agentEnterpriseId 企业ID
     * @param excludeOpportunityId 排除的机会ID
     * @return 机会列表
     */
    List<Opportunity> selectActiveOpportunitiesByTypeAndEnterprise(@Param("opportunityType") String opportunityType,
                                                                  @Param("agentEnterpriseId") Integer agentEnterpriseId,
                                                                  @Param("excludeOpportunityId") Integer excludeOpportunityId);

    /**
     * 根据机会类型和企业信用代码查询进行中的机会列表
     * @param opportunityType 机会类型
     * @param creditCode 企业信用代码
     * @param excludeOpportunityId 排除的机会ID
     * @return 机会列表
     */
    List<Opportunity> selectActiveOpportunitiesByTypeAndCreditCode(@Param("opportunityType") String opportunityType,
                                                                  @Param("creditCode") String creditCode,
                                                                  @Param("excludeOpportunityId") Integer excludeOpportunityId);

    /**
     * 批量更新机会的锁定状态
     * @param opportunityIds 机会ID列表
     * @param lockStatus 锁定状态
     * @param updateUserId 更新用户ID
     * @return 更新行数
     */
    int batchUpdateLockStatus(@Param("opportunityIds") List<Integer> opportunityIds,
                             @Param("lockStatus") Integer lockStatus,
                             @Param("updateUserId") String updateUserId);

    void deleteUnSubmitOpportunity(@Param("agentEnterpriseId") Integer agentEnterpriseId);
}