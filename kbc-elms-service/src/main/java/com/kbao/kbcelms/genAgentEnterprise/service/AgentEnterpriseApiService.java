package com.kbao.kbcelms.genAgentEnterprise.service;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONObject;import com.alibaba.nacos.common.utils.CollectionUtils;import com.github.pagehelper.PageHelper;import com.github.pagehelper.PageInfo;import com.kbao.commons.exception.BusinessException;import com.kbao.commons.web.PageRequest;import com.kbao.kbcelms.bascode.entity.BasCode;import com.kbao.kbcelms.bascode.service.BasCodeService;import com.kbao.kbcelms.businessProcess.bean.AgentEnterpriseProcessVo;import com.kbao.kbcelms.businessProcess.bean.BusinessProcessVO;import com.kbao.kbcelms.businessProcess.model.BusinessProcess;import com.kbao.kbcelms.businessProcess.service.BusinessProcessService;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseCreateRecord;import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseCreateRecordService;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;import com.kbao.kbcelms.enterpriseconfirmation.service.EnterpriseConfirmationService;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseEditVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcelms.industry.entity.Industry;import com.kbao.kbcelms.industry.service.IndustryService;import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceDetailResponse;import com.kbao.kbcelms.industrylimit.service.IndustryLimitApiService;import com.kbao.kbcelms.opportunity.service.OpportunityApiService;import com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService;import com.kbao.kbcelms.util.StringUtils;import com.kbao.kbcucs.context.RequestContext;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.EmptyUtils;import com.kbao.tool.util.MapUtils;import com.kbao.tool.util.SysLoginUtils;import jodd.util.CollectionUtil;import org.joda.time.DateTime;import org.joda.time.LocalDate;import org.joda.time.LocalDateTime;import org.springframework.beans.BeanUtils;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;import java.util.*;import java.util.function.Function;import java.util.stream.Collectors;
@Service
public class AgentEnterpriseApiService {
    @Autowired
    private GenAgentEnterpriseMapper genAgentEnterpriseMapper;
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    @Autowired
    private EnterpriseQueryRecordService enterpriseQueryRecordService;
    @Autowired
    private OpportunityApiService opportunityApiService;
    @Autowired
    private EnterpriseConfirmationService enterpriseConfirmationService;
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private EnterpriseCreateRecordService enterpriseCreateRecordService;
    @Autowired
    private BusinessProcessService businessProcessService;
    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;
    @Autowired
    private IndustryService industryService;
    @Autowired
    private IndustryLimitApiService industryLimitApiService;
    @Autowired
    private BasCodeService basCodeService;

    public PageInfo<GenAgentEnterprise> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        Map<String,String> typeMap = enterpriseTypeService.getEnterpriseTypeMap();

        AgentEnterpriseListReqVo param = pageRequest.getParam();
        param.setAgentCode(userInfo.getAgentCode());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<GenAgentEnterprise> enterpriseList = genAgentEnterpriseMapper.getEnterpriseListByAgentCode(param);
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
            enterpriseList.forEach(enterprise -> {
                String categoryName = enterprise.getCategoryName();
                if (categoryName != null && categoryName.contains("/")) {
                    categoryName = categoryName.substring(categoryName.indexOf("/") + 1);
                    enterprise.setCategoryName(categoryName);
                }
                enterprise.setDtType(typeMap.get(enterprise.getDtType()));
                Date verifyTime = enterprise.getVerifyTime();
                if (verifyTime == null || LocalDateTime.fromDateFields(verifyTime).plusYears(1).isBefore(LocalDateTime.now())){
                    enterprise.setIsVerified("0");
                }
            });
        }
        return new PageInfo<>(enterpriseList);
    }

    public GenAgentEnterprise getById(Integer id) {
        GenAgentEnterprise enterprise = genAgentEnterpriseMapper.selectByPrimaryKey(id);
        Date verifyTime = enterprise.getVerifyTime();

        enterprise.setIsVerifyOverOneYear("0");
        if ("1".equals(enterprise.getIsVerified()) &&
            (verifyTime == null || LocalDateTime.fromDateFields(verifyTime).plusYears(1).isBefore(LocalDateTime.now()))){
            enterprise.setIsVerified("0");
            enterprise.setIsVerifyOverOneYear("1");
        }
        String categoryName = enterprise.getCategoryName();
        if (categoryName != null && categoryName.contains("/")) {
            categoryName = categoryName.substring(categoryName.indexOf("/") + 1);
            enterprise.setMinCategoryName(categoryName);
        }
        return enterprise;
    }

    public Map<String, Object> saveAgentEnterprise(AgentEnterpriseEditVo editVo) {
        // todo 添加行业校验逻辑
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        String queryRecordId = editVo.getQueryRecordId();
        EnterpriseQueryRecord queryRecord = null;
        String isVerified = "0";
        boolean isFirstVerify = false;
        if (queryRecordId != null) {
            queryRecord = enterpriseQueryRecordService.findById(queryRecordId);
            if (queryRecord != null) {
                isVerified = "1";
            }
        }
        if (EmptyUtils.isNotEmpty(editVo.getCategoryCode())) {
            Industry industry = industryService.selectByCode(editVo.getCategoryCode());
            editVo.setCategoryName(industry.getFullName());
        }
        if (EmptyUtils.isNotEmpty(editVo.getCategoryCode())) {
            BasCode basCode = basCodeService.getByCode(editVo.getDistrictCode());
            editVo.setCity(basCode.getFullPathName());
        }
        String dtType = StringUtils.getDtType(editVo.getStaffScale(), editVo.getAnnualIncome());
        GenAgentEnterprise enterprise;
        if (editVo.getId() == null) {
            enterprise = new GenAgentEnterprise();
            enterprise.setAgentCode(userInfo.getAgentCode());
            enterprise.setAgentName(userInfo.getAgentName());
            enterprise.setLegalCode(userInfo.getLegalCode());
            enterprise.setLegalName(userInfo.getLegalName());
            enterprise.setTradingCenterCode(userInfo.getTradingCenterCode());
            enterprise.setTradingCenterName(userInfo.getTradingCenterName());
            enterprise.setTenantId(RequestContext.TenantId.get());
            enterprise.setCreateId(userInfo.getId());
            enterprise.setIsVerified("0");
        } else {
            enterprise = genAgentEnterpriseMapper.selectByPrimaryKey(editVo.getId());
            if (queryRecordId == null) {
                //防止被替换掉
                editVo.setQueryRecordId(enterprise.getQueryRecordId());
            }
        }
        BeanUtils.copyProperties(editVo, enterprise);
        enterprise.setDtType(dtType);

        if ("1".equals(isVerified)) {
            enterprise.setIsVerified(isVerified);
            enterprise.setVerifyTime(queryRecord.getVerifyTime());
            enterprise.setQueryRecordId(queryRecord.getId());
        }
        JSONObject result = new JSONObject();
        if (enterprise.getId() == null) {
            boolean isBlocked = industryLimitApiService.isServiceLimit(enterprise);

            boolean isCreate = false;
            if (queryRecord != null) {
                EnterpriseCreateRecord createRecord = enterpriseCreateRecordService.findByQueryId(queryRecord.getId());
                if (createRecord != null) {
                    JSONObject inputDate = JSONObject.parseObject(JSON.toJSONString(enterprise));
                    createRecord.setInputData(inputDate);
                    createRecord.setIsBlocked(isBlocked);
                    enterpriseCreateRecordService.update(createRecord);
                    isCreate = true;
                }
            }
            if (!isCreate) {
                enterpriseCreateRecordService.recordCreateLog(editVo.getName(), userInfo, enterprise, null, false,null, isBlocked);
            }
            if (isBlocked) {
                result.put("errorCode", 3);
                return result;
            }
            int isExists = genAgentEnterpriseMapper.isExistAgentEnterprise(userInfo.getAgentCode(), editVo.getCreditCode());
            if (isExists > 0) {
                result.put("errorCode", 6);
                return result;
            }
            isFirstVerify = "1".equals(isVerified);
            genAgentEnterpriseMapper.insert(enterprise);
        } else {
            isFirstVerify = "0".equals(enterprise.getIsVerified()) && "1".equals(isVerified);
            genAgentEnterpriseMapper.updateByPrimaryKey(enterprise);
        }
        // 确权逻辑
        if (isFirstVerify) {
            EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.findByCreditCode(enterprise.getCreditCode());
            enterpriseConfirmationService.insertConfirmationRecord(basicInfo);
        }
        return MapUtils.objectToMap(enterprise);
    }

    public Map<String, Object> delete(Integer id) {
        Map<String, Object> result = new HashMap<>();
        boolean hasSubmitOppo = opportunityApiService.getSubmitOpportunityNum(id);
        if (hasSubmitOppo) {
            result.put("errorCode", 1);
            result.put("errorMsg", "有进行中的机会，无法删除企业客户");
            return result;
        }
        genAgentEnterpriseMapper.deleteByPrimaryKey(id);
        opportunityApiService.deleteUnSubmitOpportunity(id);
        return result;
    }

    public Map<String, Object> getAgentEnterpriseProcess(AgentEnterpriseProcessVo processVo) {
        Map<String, Object> result = new HashMap<>();
        List<String> processList = processVo.getProcessList();
        if (EmptyUtils.isEmpty(processList)) {
            return result;
        }
        // 0-未解锁，1-立即验真，2-收集问卷，3-生成报告
        GenAgentEnterprise enterprise = genAgentEnterpriseMapper.selectByPrimaryKey(processVo.getAgentEnterpriseId());
        Map<String,EnterpriseServiceDetailResponse.ServiceDetail> serviceMap = industryLimitApiService.getServiceMap(enterprise);
        Set<String> enterpriseTypes = questionnaireAnswerService.getEnterpriseTypesByAnswer(processVo.getAgentEnterpriseId());
        boolean hasQuestionnaireAnswer = enterpriseTypes.contains(enterprise.getDtType());
        List<BusinessProcessVO> allBusinessProcess = businessProcessService.getAllBusinessProcess();
        Map<String,BusinessProcessVO> allMap = allBusinessProcess.stream().collect(Collectors.toMap(BusinessProcessVO::getBusinessCode, Function.identity(), (k1, k2) -> k1));
        Map<String, Object> businessMap = new HashMap<>();
        for (String businessCode : processList) {
            BusinessProcessVO item = allMap.get(businessCode);
            List<String> configs = item.getProcessConfigs();
            String step = "0";
            EnterpriseServiceDetailResponse.ServiceDetail serviceDetail = serviceMap.get(businessCode);
            if (serviceDetail != null && !serviceDetail.getIsLocked()) {
                if (EmptyUtils.isNotEmpty(configs)) {
                    if (configs.contains(BusinessProcess.ProcessCode.RISK_MANAGEMENT)
                        && (hasQuestionnaireAnswer || !configs.contains(BusinessProcess.ProcessCode.QUESTIONNAIRE_COLLECTION))
                        && "1".equals(enterprise.getIsVerified())) {
                        step = "3";
                    } else if ("1".equals(enterprise.getIsVerified()) && !hasQuestionnaireAnswer
                        && configs.contains(BusinessProcess.ProcessCode.QUESTIONNAIRE_COLLECTION)) {
                        step = "2";
                    } else if (!"1".equals(enterprise.getIsVerified())) {
                        step = "1";
                    }
                }
            }
            businessMap.put(businessCode, step);
        }
        String hasOpportunity = opportunityApiService.getAgentOpportunityNum(processVo.getAgentEnterpriseId()) ? "1" : "0";
        result.put("hasOpportunity", hasOpportunity);
        result.put("business", businessMap);
        return result;
    }

    public boolean isExistAgentEnterprise(String agentCode, String creditCode) {
        return genAgentEnterpriseMapper.isExistAgentEnterprise(agentCode, creditCode) > 0;
    }
}
