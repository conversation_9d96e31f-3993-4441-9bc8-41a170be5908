package com.kbao.kbcelms.enterpriseopplock.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import com.kbao.kbcelms.enterpriseopplock.vo.EnterpriseOppLockVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业机会锁定Mapper
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface EnterpriseOppLockMapper extends BaseMapper<EnterpriseOppLock, Integer> {
    
    /**
     * 分页查询企业机会锁定列表
     * @param queryDTO 查询条件
     * @return 企业机会锁定列表
     */
    List<EnterpriseOppLockVO> selectPageList(EnterpriseOppLockQueryDTO queryDTO);
    
    /**
     * 统计企业机会锁定总数
     * @param queryDTO 查询条件
     * @return 总数
     */
    Long countList(EnterpriseOppLockQueryDTO queryDTO);
    
    /**
     * 根据机会ID查询锁定记录
     * @param opportunityId 机会ID
     * @return 锁定记录列表
     */
    List<EnterpriseOppLockVO> selectByOpportunityId(@Param("opportunityId") Integer opportunityId);
    
    /**
     * 根据社会统一信用代码查询锁定记录
     * @param creditCode 社会统一信用代码
     * @return 锁定记录列表
     */
    List<EnterpriseOppLockVO> selectByCreditCode(@Param("creditCode") String creditCode);
    
    /**
     * 根据机会ID和信用代码查询锁定记录
     * @param opportunityId 机会ID
     * @param creditCode 社会统一信用代码
     * @return 锁定记录
     */
    EnterpriseOppLockVO selectByOpportunityIdAndCreditCode(@Param("opportunityId") Integer opportunityId, 
                                                          @Param("creditCode") String creditCode);
    
    /**
     * 逻辑删除
     * @param id 主键
     * @param updateId 更新人
     * @return 影响行数
     */
    int deleteLogical(@Param("id") Integer id, @Param("updateId") String updateId);
    
    /**
     * 根据机会类型和社会统一信用代码查询锁定记录列表
     * @param opportunityType 机会类型
     * @param creditCode 社会统一信用代码
     * @return 锁定记录列表
     */
    List<EnterpriseOppLockVO> selectByOpportunityTypeAndCreditCode(@Param("opportunityType") String opportunityType, @Param("creditCode") String creditCode);

    int hasLockOpp(@Param("creditCode") String creditCode, @Param("opportunityType") String opportunityType);
}
