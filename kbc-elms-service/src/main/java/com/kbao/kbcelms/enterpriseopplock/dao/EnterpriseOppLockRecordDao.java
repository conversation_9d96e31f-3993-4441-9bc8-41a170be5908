package com.kbao.kbcelms.enterpriseopplock.dao;

import com.kbao.kbcelms.common.nosql.dao.TenantMongoDaoImpl;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 企业机会锁定记录MongoDB DAO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public class EnterpriseOppLockRecordDao extends TenantMongoDaoImpl<EnterpriseOppLockRecord, String> {
    
    /**
     * 根据锁定主表ID查询记录列表
     * @param lockId 锁定主表ID
     * @return 记录列表
     */
    public List<EnterpriseOppLockRecord> findByLockId(Integer lockId) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lock_id").is(lockId)
                .and("is_deleted").is(0));
        return this.find(query);
    }
    
    /**
     * 根据锁定主表ID查询最新有效记录
     * @param lockId 锁定主表ID
     * @return 最新有效记录
     */
    public EnterpriseOppLockRecord findLatestValidByLockId(Integer lockId) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lock_id").is(lockId)
                .and("status").is(1)
                .and("is_deleted").is(0));
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "create_time"));
        query.limit(1);
        return this.findOne(query);
    }
    
    /**
     * 根据锁定主表ID批量查询最新有效记录
     * @param lockIds 锁定主表ID列表
     * @return 最新有效记录列表
     */
    public List<EnterpriseOppLockRecord> findLatestValidByLockIds(List<Integer> lockIds) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lock_id").in(lockIds)
                .and("status").is(1)
                .and("is_deleted").is(0));
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "create_time"));
        return this.find(query);
    }
    
    /**
     * 根据锁定人查询记录列表
     * @param lockUser 锁定人
     * @return 记录列表
     */
    public List<EnterpriseOppLockRecord> findByLockUser(String lockUser) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lock_user").is(lockUser)
                .and("is_deleted").is(0));
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "create_time"));
        return this.find(query);
    }
    
    /**
     * 设置记录状态为无效
     * @param lockId 锁定主表ID
     * @param updateId 更新人
     * @return 更新结果
     */
    public long invalidateByLockId(Integer lockId, String updateId) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("lock_id").is(lockId)
                .and("status").is(1)
                .and("is_deleted").is(0));
        
        org.springframework.data.mongodb.core.query.Update update = new org.springframework.data.mongodb.core.query.Update();
        update.set("status", 0);
        update.set("update_id", updateId);
        update.set("update_time", new java.util.Date());
        
        return this.update(query, update).getModifiedCount();
    }
    
    /**
     * 逻辑删除记录
     * @param id 主键
     * @param updateId 更新人
     * @return 更新结果
     */
    public long deleteLogicalById(String id, String updateId) {
        Query query = new Query();
        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("id").is(id));
        
        org.springframework.data.mongodb.core.query.Update update = new org.springframework.data.mongodb.core.query.Update();
        update.set("is_deleted", 1);
        update.set("update_id", updateId);
        update.set("update_time", new java.util.Date());
        
        return this.update(query, update).getModifiedCount();
    }
}
