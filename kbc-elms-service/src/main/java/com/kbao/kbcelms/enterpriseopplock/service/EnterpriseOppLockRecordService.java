package com.kbao.kbcelms.enterpriseopplock.service;

import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import com.kbao.kbcelms.enterpriseopplock.dao.EnterpriseOppLockRecordDao;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordAddDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLockRecord;
import com.kbao.kbcelms.enterpriseopplock.vo.EnterpriseOppLockRecordVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业机会锁定记录Service（不进行租户隔离）
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class EnterpriseOppLockRecordService {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockRecordService.class);
    
    @Autowired
    private EnterpriseOppLockRecordDao dao;
    
    /**
     * 分页查询企业机会锁定记录列表
     * @param pageRequest 分页请求参数
     * @return 分页结果
     */
    public Result<com.github.pagehelper.PageInfo<EnterpriseOppLockRecordVO>> getPageList(PageRequest<EnterpriseOppLockRecordQueryDTO> pageRequest) {
        try {
            EnterpriseOppLockRecordQueryDTO queryDTO = pageRequest.getParam();
            if (queryDTO == null) {
                queryDTO = new EnterpriseOppLockRecordQueryDTO();
            }
            
            // 构建查询条件
            Query query = buildQuery(queryDTO);
            
            // 设置分页和排序
            query.with(Sort.by(Sort.Direction.DESC, "create_time"));
            query.skip((long) (pageRequest.getPageNum() - 1) * pageRequest.getPageSize());
            query.limit(pageRequest.getPageSize());
            List<EnterpriseOppLockRecord> records = this.dao.find(query);
            Query countQuery = buildQuery(queryDTO);
            long total = this.dao.count(countQuery);
            
            // 构建分页信息
            com.github.pagehelper.PageInfo<EnterpriseOppLockRecord> mongoPageInfo = new com.github.pagehelper.PageInfo<>();
            mongoPageInfo.setList(records);
            mongoPageInfo.setTotal(total);
            mongoPageInfo.setPageNum(pageRequest.getPageNum());
            mongoPageInfo.setPageSize(pageRequest.getPageSize());
            mongoPageInfo.setPages((int) Math.ceil((double) total / pageRequest.getPageSize()));
            
            // 转换为VO
            List<EnterpriseOppLockRecordVO> voList = mongoPageInfo.getList().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
            
            // 构建返回的PageInfo
            com.github.pagehelper.PageInfo<EnterpriseOppLockRecordVO> pageInfo = new com.github.pagehelper.PageInfo<>();
            pageInfo.setList(voList);
            pageInfo.setTotal(mongoPageInfo.getTotal());
            pageInfo.setPageNum(mongoPageInfo.getPageNum());
            pageInfo.setPageSize(mongoPageInfo.getPageSize());
            pageInfo.setPages(mongoPageInfo.getPages());
            
            return Result.succeed(pageInfo, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询企业机会锁定记录列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询企业机会锁定记录详情
     * @param id 主键ID
     * @return 详情信息
     */
    public Result<EnterpriseOppLockRecordVO> getById(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                return Result.failed("ID不能为空");
            }
            
            EnterpriseOppLockRecord entity = this.dao.findById(id);
            if (entity == null) {
                return Result.failed("记录不存在");
            }
            
            EnterpriseOppLockRecordVO vo = convertToVO(entity);
            
            return Result.succeed(vo, "查询成功");
        } catch (Exception e) {
            logger.error("根据ID查询企业机会锁定记录详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 新增企业机会锁定记录
     * @param addDTO 新增参数
     * @return 新增结果
     */
    public Result<String> add(EnterpriseOppLockRecordAddDTO addDTO) {
        try {
            // 验证文件数量限制
            if (addDTO.getLockFiles() != null && addDTO.getLockFiles().size() > 10) {
                return Result.failed("锁定文件数量不能超过10个");
            }
            
            EnterpriseOppLockRecord entity = new EnterpriseOppLockRecord();
            BeanUtils.copyProperties(addDTO, entity);
            
            // 转换文件信息
            if (addDTO.getLockFiles() != null && !addDTO.getLockFiles().isEmpty()) {
                List<EnterpriseOppLockRecord.LockFileInfo> lockFileInfos = addDTO.getLockFiles().stream()
                    .map(this::convertToLockFileInfo)
                    .collect(Collectors.toList());
                entity.setLockFiles(lockFileInfos);
            }
            
            // 设置创建信息
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            Date now = new Date();
            entity.setCreateId(currentUserId);
            entity.setCreateTime(now);
            entity.setUpdateId(currentUserId);
            entity.setUpdateTime(now);
            entity.setIsDeleted(0);
            entity.setStatus(1); // 默认状态为有效
            
            // 如果没有设置锁定时间，默认为当前时间
            if (entity.getLockTime() == null) {
                entity.setLockTime(now);
            }
            
            EnterpriseOppLockRecord savedEntity = this.dao.save(entity);
            if (savedEntity != null && savedEntity.getId() != null) {
                return Result.succeed(savedEntity.getId(), "新增成功");
            } else {
                return Result.failed("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增企业机会锁定记录失败", e);
            return Result.failed("新增失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据锁定主表ID查询记录列表
     * @param lockId 锁定主表ID
     * @return 记录列表
     */
    public Result<List<EnterpriseOppLockRecordVO>> getByLockId(Integer lockId) {
        try {
            if (lockId == null) {
                return Result.failed("锁定主表ID不能为空");
            }
            
            List<EnterpriseOppLockRecord> entities = this.dao.findByLockId(lockId);
            
            List<EnterpriseOppLockRecordVO> voList = entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
            
            return Result.succeed(voList, "查询成功");
        } catch (Exception e) {
            logger.error("根据锁定主表ID查询记录列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据锁定主表ID查询最新有效记录
     * @param lockId 锁定主表ID
     * @return 最新有效记录
     */
    public Result<EnterpriseOppLockRecordVO> getLatestValidByLockId(Integer lockId) {
        try {
            if (lockId == null) {
                return Result.failed("锁定主表ID不能为空");
            }
            
            EnterpriseOppLockRecord entity = this.dao.findLatestValidByLockId(lockId);
            EnterpriseOppLockRecordVO vo = null;
            if (entity != null) {
                vo = convertToVO(entity);
            }
            
            return Result.succeed(vo, "查询成功");
        } catch (Exception e) {
            logger.error("根据锁定主表ID查询最新有效记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据锁定主表ID批量查询最新有效记录
     * @param lockIds 锁定主表ID列表
     * @return 最新有效记录列表
     */
    public Result<List<EnterpriseOppLockRecordVO>> getLatestValidByLockIds(List<Integer> lockIds) {
        try {
            if (lockIds == null || lockIds.isEmpty()) {
                return Result.failed("锁定主表ID列表不能为空");
            }
            
            List<EnterpriseOppLockRecord> entities = this.dao.findLatestValidByLockIds(lockIds);
            
            List<EnterpriseOppLockRecordVO> voList = entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
            
            return Result.succeed(voList, "查询成功");
        } catch (Exception e) {
            logger.error("根据锁定主表ID批量查询最新有效记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 设置记录状态为无效
     * @param lockId 锁定主表ID
     * @return 操作结果
     */
    public Result<Boolean> invalidateByLockId(Integer lockId) {
        try {
            if (lockId == null) {
                return Result.failed("锁定主表ID不能为空");
            }
            
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            long result = this.dao.invalidateByLockId(lockId, currentUserId);
            
            return Result.succeed(result > 0, result > 0 ? "操作成功" : "无有效记录需要处理");
        } catch (Exception e) {
            logger.error("设置记录状态为无效失败", e);
            return Result.failed("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除企业机会锁定记录
     * @param id 主键ID
     * @return 删除结果
     */
    public Result<Boolean> deleteById(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                return Result.failed("ID不能为空");
            }
            
            // 检查记录是否存在
            EnterpriseOppLockRecord existingEntity = this.dao.findById(id);
            if (existingEntity == null) {
                return Result.failed("记录不存在");
            }
            
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            long result = this.dao.deleteLogicalById(id, currentUserId);
            if (result > 0) {
                return Result.succeed(true, "删除成功");
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除企业机会锁定记录失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建查询条件（不进行租户隔离）
     * @param queryDTO 查询DTO
     * @return MongoDB查询对象
     */
    private Query buildQuery(EnterpriseOppLockRecordQueryDTO queryDTO) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        
        // 添加基础过滤条件（移除租户隔离逻辑）
        criteria.and("is_deleted").is(0);
        
        if (queryDTO.getLockId() != null) {
            criteria.and("lock_id").is(queryDTO.getLockId());
        }
        
        if (queryDTO.getCreditCode() != null && !queryDTO.getCreditCode().trim().isEmpty()) {
            criteria.and("creditCode").is(queryDTO.getCreditCode());
        }
        
        if (queryDTO.getOpportunityType() != null && !queryDTO.getOpportunityType().trim().isEmpty()) {
            criteria.and("opportunityType").is(queryDTO.getOpportunityType());
        }
        
        if (queryDTO.getLockUser() != null && !queryDTO.getLockUser().trim().isEmpty()) {
            criteria.and("lockUser").regex(queryDTO.getLockUser(), "i");
        }
        
        if (queryDTO.getStatus() != null) {
            criteria.and("status").is(queryDTO.getStatus());
        }
        
        if (queryDTO.getLockTimeStart() != null) {
            criteria.and("lock_time").gte(queryDTO.getLockTimeStart());
        }
        
        if (queryDTO.getLockTimeEnd() != null) {
            criteria.and("lock_time").lte(queryDTO.getLockTimeEnd());
        }
        
        if (queryDTO.getLockEndTimeStart() != null) {
            criteria.and("lock_end_time").gte(queryDTO.getLockEndTimeStart());
        }
        
        if (queryDTO.getLockEndTimeEnd() != null) {
            criteria.and("lock_end_time").lte(queryDTO.getLockEndTimeEnd());
        }
        
        if (queryDTO.getCreateTimeStart() != null) {
            criteria.and("create_time").gte(queryDTO.getCreateTimeStart());
        }
        
        if (queryDTO.getCreateTimeEnd() != null) {
            criteria.and("create_time").lte(queryDTO.getCreateTimeEnd());
        }
        
        query.addCriteria(criteria);
        return query;
    }
    
    /**
     * 转换Entity为VO
     * @param entity 实体对象
     * @return VO对象
     */
    private EnterpriseOppLockRecordVO convertToVO(EnterpriseOppLockRecord entity) {
        EnterpriseOppLockRecordVO vo = new EnterpriseOppLockRecordVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 转换文件信息
        if (entity.getLockFiles() != null && !entity.getLockFiles().isEmpty()) {
            List<EnterpriseOppLockRecordVO.LockFileInfo> voFileInfos = entity.getLockFiles().stream()
                .map(this::convertToVOFileInfo)
                .collect(Collectors.toList());
            vo.setLockFiles(voFileInfos);
        }
        
        return vo;
    }
    
    /**
     * 转换DTO文件信息为Entity文件信息
     * @param dtoFileInfo DTO文件信息
     * @return Entity文件信息
     */
    private EnterpriseOppLockRecord.LockFileInfo convertToLockFileInfo(EnterpriseOppLockRecordAddDTO.LockFileInfo dtoFileInfo) {
        EnterpriseOppLockRecord.LockFileInfo entityFileInfo = new EnterpriseOppLockRecord.LockFileInfo();
        BeanUtils.copyProperties(dtoFileInfo, entityFileInfo);
        
        // 如果没有设置上传时间，使用当前时间
        if (entityFileInfo.getUploadTime() == null) {
            entityFileInfo.setUploadTime(new Date());
        }
        
        return entityFileInfo;
    }
    
    /**
     * 转换Entity文件信息为VO文件信息
     * @param entityFileInfo Entity文件信息
     * @return VO文件信息
     */
    private EnterpriseOppLockRecordVO.LockFileInfo convertToVOFileInfo(EnterpriseOppLockRecord.LockFileInfo entityFileInfo) {
        EnterpriseOppLockRecordVO.LockFileInfo voFileInfo = new EnterpriseOppLockRecordVO.LockFileInfo();
        BeanUtils.copyProperties(entityFileInfo, voFileInfo);
        return voFileInfo;
    }
}
