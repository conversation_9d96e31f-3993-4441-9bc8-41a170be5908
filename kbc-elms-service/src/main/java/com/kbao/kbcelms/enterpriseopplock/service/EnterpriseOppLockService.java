package com.kbao.kbcelms.enterpriseopplock.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.enterpriseopplock.dao.EnterpriseOppLockMapper;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockAddDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockUpdateDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.OpportunityLockDTO;
import com.kbao.kbcelms.enterpriseopplock.entity.EnterpriseOppLock;
import com.kbao.kbcelms.enterpriseopplock.vo.EnterpriseOppLockVO;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockRecordService;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordAddDTO;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业机会锁定Service
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class EnterpriseOppLockService extends BaseSQLServiceImpl<EnterpriseOppLock, Integer, EnterpriseOppLockMapper> {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseOppLockService.class);
    
    @Autowired
    private OpportunityService opportunityService;
    
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    
    @Autowired
    private EnterpriseOppLockRecordService enterpriseOppLockRecordService;
    
    /**
     * 分页查询企业机会锁定列表
     * @param pageRequest 分页请求参数
     * @return 分页结果
     */
    public Result<PageInfo<EnterpriseOppLockVO>> getPageList(PageRequest<EnterpriseOppLockQueryDTO> pageRequest) {
        try {
            EnterpriseOppLockQueryDTO queryDTO = pageRequest.getParam();
            if (queryDTO == null) {
                queryDTO = new EnterpriseOppLockQueryDTO();
            }
            
            PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
            List<EnterpriseOppLockVO> list = this.mapper.selectPageList(queryDTO);
            PageInfo<EnterpriseOppLockVO> pageInfo = new PageInfo<>(list);
            
            return Result.succeed(pageInfo, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询企业机会锁定列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询企业机会锁定详情
     * @param id 主键ID
     * @return 详情信息
     */
    public Result<EnterpriseOppLockVO> getById(Integer id) {
        try {
            if (id == null) {
                return Result.failed("ID不能为空");
            }
            
            EnterpriseOppLock entity = this.selectByPrimaryKey(id);
            if (entity == null) {
                return Result.failed("记录不存在");
            }
            
            EnterpriseOppLockVO vo = new EnterpriseOppLockVO();
            BeanUtils.copyProperties(entity, vo);
            
            return Result.succeed(vo, "查询成功");
        } catch (Exception e) {
            logger.error("查询企业机会锁定详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 新增企业机会锁定
     * @param addDTO 新增参数
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Integer> add(EnterpriseOppLockAddDTO addDTO) {
        try {
            // 检查是否已存在相同的锁定记录
            EnterpriseOppLockVO existingLock = this.mapper.selectByOpportunityIdAndCreditCode(
                addDTO.getOpportunityId(), addDTO.getCreditCode());
            if (existingLock != null) {
                return Result.failed("该企业在此机会下已存在锁定记录");
            }
            
            EnterpriseOppLock entity = new EnterpriseOppLock();
            BeanUtils.copyProperties(addDTO, entity);
            
            // 设置创建信息
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            Date now = new Date();
            entity.setCreateId(currentUserId);
            entity.setCreateTime(now);
            entity.setUpdateId(currentUserId);
            entity.setUpdateTime(now);
            entity.setIsDeleted(0);
            
            // 如果没有设置锁定时间，默认为当前时间
            if (entity.getLockTime() == null) {
                entity.setLockTime(now);
            }
            
            int result = this.insertSelective(entity);
            if (result > 0) {
                return Result.succeed(entity.getId(), "新增成功");
            } else {
                return Result.failed("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增企业机会锁定失败", e);
            return Result.failed("新增失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新企业机会锁定
     * @param updateDTO 更新参数
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> update(EnterpriseOppLockUpdateDTO updateDTO) {
        try {
            // 检查记录是否存在
            EnterpriseOppLock existingEntity = this.selectByPrimaryKey(updateDTO.getId());
            if (existingEntity == null) {
                return Result.failed("记录不存在");
            }
            
            EnterpriseOppLock entity = new EnterpriseOppLock();
            BeanUtils.copyProperties(updateDTO, entity);
            
            // 设置更新信息
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            entity.setUpdateId(currentUserId);
            entity.setUpdateTime(new Date());
            
            int result = this.updateByPrimaryKeySelective(entity);
            if (result > 0) {
                return Result.succeed(true, "更新成功");
            } else {
                return Result.failed("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新企业机会锁定失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除企业机会锁定
     * @param id 主键ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteById(Integer id) {
        try {
            if (id == null) {
                return Result.failed("ID不能为空");
            }
            
            // 检查记录是否存在
            EnterpriseOppLock existingEntity = this.selectByPrimaryKey(id);
            if (existingEntity == null) {
                return Result.failed("记录不存在");
            }
            
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            int result = this.mapper.deleteLogical(id, currentUserId);
            if (result > 0) {
                return Result.succeed(true, "删除成功");
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除企业机会锁定失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据机会ID查询锁定记录
     * @param opportunityId 机会ID
     * @return 锁定记录列表
     */
    public Result<List<EnterpriseOppLockVO>> getByOpportunityId(Integer opportunityId) {
        try {
            if (opportunityId == null) {
                return Result.failed("机会ID不能为空");
            }
            
            List<EnterpriseOppLockVO> list = this.mapper.selectByOpportunityId(opportunityId);
            return Result.succeed(list, "查询成功");
        } catch (Exception e) {
            logger.error("根据机会ID查询锁定记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据社会统一信用代码查询锁定记录
     * @param creditCode 社会统一信用代码
     * @return 锁定记录列表
     */
    public Result<List<EnterpriseOppLockVO>> getByCreditCode(String creditCode) {
        try {
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("社会统一信用代码不能为空");
            }
            
            List<EnterpriseOppLockVO> list = this.mapper.selectByCreditCode(creditCode);
            return Result.succeed(list, "查询成功");
        } catch (Exception e) {
            logger.error("根据社会统一信用代码查询锁定记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查企业是否被锁定
     * @param opportunityId 机会ID
     * @param creditCode 社会统一信用代码
     * @return 是否被锁定
     */
    public Result<Boolean> checkLocked(Integer opportunityId, String creditCode) {
        try {
            if (opportunityId == null) {
                return Result.failed("机会ID不能为空");
            }
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("社会统一信用代码不能为空");
            }

            EnterpriseOppLockVO lockRecord = this.mapper.selectByOpportunityIdAndCreditCode(opportunityId, creditCode);
            boolean isLocked = false;
            String message = "企业未被锁定";

            if (lockRecord != null) {
                Date now = new Date();
                Date lockEndTime = lockRecord.getLockEndTime();

                // 如果有锁定截止时间，检查是否已过期
                if (lockEndTime != null) {
                    if (now.after(lockEndTime)) {
                        // 锁定已过期，自动删除锁定记录
                        this.mapper.deleteLogical(lockRecord.getId(), "system");
                        isLocked = false;
                        message = "企业锁定已过期，已自动解锁";
                    } else {
                        isLocked = true;
                        message = String.format("企业已被锁定，锁定截止时间：%s", lockEndTime.toString());
                    }
                } else {
                    // 没有截止时间，永久锁定
                    isLocked = true;
                    message = "企业已被永久锁定";
                }
            }

            return Result.succeed(isLocked, message);
        } catch (Exception e) {
            logger.error("检查企业锁定状态失败", e);
            return Result.failed("检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查并清理过期的锁定记录
     * @return 清理的记录数量
     */
    public Result<Integer> cleanExpiredLocks() {
        try {
            // 这里可以实现清理过期锁定的逻辑
            // 由于需要复杂的查询，这里先返回成功
            logger.info("开始清理过期锁定记录");
            return Result.succeed(0, "清理完成");
        } catch (Exception e) {
            logger.error("清理过期锁定记录失败", e);
            return Result.failed("清理失败：" + e.getMessage());
        }
    }
    
    /**
     * 锁定机会
     * @param lockDTO 锁定参数
     * @return 锁定结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> lockOpportunity(OpportunityLockDTO lockDTO) {
        try {
            if (lockDTO.getOpportunityId() == null) {
                return Result.failed("机会ID不能为空");
            }
            
            Integer opportunityId = lockDTO.getOpportunityId();
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            Date currentTime = new Date();
            
            // 1. 查询机会信息，获取机会类型和关联企业信息
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity == null) {
                return Result.failed("机会不存在");
            }
            
            String opportunityType = opportunity.getOpportunityType();
            if (opportunityType == null || opportunityType.trim().isEmpty()) {
                return Result.failed("机会类型不能为空");
            }
            
            // 查询关联企业信息
            GenAgentEnterprise enterprise = null;
            String creditCode = null;
            String enterpriseName = null;
            
            if (opportunity.getAgentEnterpriseId() != null) {
                enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
                if (enterprise != null) {
                    creditCode = enterprise.getCreditCode();
                    enterpriseName = enterprise.getName();
                }
            }
            
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("机会关联的企业信息不完整，无法进行锁定");
            }
            
            // 2. 检查当前机会是否已被锁定
            EnterpriseOppLockVO existingLock = this.mapper.selectByOpportunityIdAndCreditCode(opportunityId, creditCode);
            if (existingLock != null && !isLockExpired(existingLock.getLockEndTime())) {
                return Result.failed("该机会已被锁定，无法进行锁定操作");
            }
            
            // 3. 检查同机会类型+同企业的其他机会是否已被锁定且未过期
            List<EnterpriseOppLockVO> sameTypeAndEnterpriseLocks = this.mapper.selectByOpportunityTypeAndCreditCode(opportunityType, creditCode);
            for (EnterpriseOppLockVO lock : sameTypeAndEnterpriseLocks) {
                // 排除当前机会本身
                if (!lock.getOpportunityId().equals(opportunityId) && !isLockExpired(lock.getLockEndTime())) {
                    return Result.failed(String.format("该企业在相同机会类型下已有未过期的锁定记录（机会ID：%d），无法进行锁定", lock.getOpportunityId()));
                }
            }
            
            // 4. 创建新的锁定记录
            EnterpriseOppLock newLock = new EnterpriseOppLock();
            newLock.setOpportunityId(opportunityId);
            newLock.setOpportunityType(opportunityType);
            newLock.setName(enterpriseName);
            newLock.setCreditCode(creditCode);
            newLock.setLockUser(currentUserId);
            newLock.setLockTime(currentTime);
            
            // 设置锁定截止时间
            if (lockDTO.getLockEndTime() != null) {
                newLock.setLockEndTime(lockDTO.getLockEndTime());
            } else {
                // 默认锁定7天
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentTime);
                calendar.add(Calendar.DAY_OF_MONTH, 7);
                newLock.setLockEndTime(calendar.getTime());
            }
            
            newLock.setCreateId(currentUserId);
            newLock.setCreateTime(currentTime);
            newLock.setUpdateId(currentUserId);
            newLock.setUpdateTime(currentTime);
            newLock.setIsDeleted(0);
            
            // 保存锁定记录
            Integer lockResult = super.insertSelective(newLock);
            if (lockResult == null || lockResult <= 0) {
                return Result.failed("创建锁定记录失败");
            }
            
            // 5. 更新机会表的锁定状态
            // 将当前机会状态设置为已锁定(1)
            updateOpportunityLockStatus(opportunityId, 1); // 1-已锁定
            
            // 同机会类型+同企业的其他进行中机会设置为被锁定(2)
            updateOtherOpportunitiesLockStatus(opportunityType, creditCode, opportunityId, 2); // 2-被锁定
            
            // 6. 创建锁定记录到MongoDB
            if (lockDTO.getLockFiles() != null && !lockDTO.getLockFiles().isEmpty()) {
                // 创建MongoDB记录
                String recordId = createLockRecord(newLock.getId(), lockDTO, creditCode, opportunityType);
                if (recordId != null) {
                    // 更新主表的current_record_id
                    newLock.setCurrentRecordId(recordId);
                    super.updateByPrimaryKeySelective(newLock);
                }
            }
            
            return Result.succeed(newLock.getId().toString(), "机会锁定成功");
            
        } catch (Exception e) {
            logger.error("锁定机会失败", e);
            return Result.failed("锁定机会失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查锁定记录是否已过期
     * @param lockEndTime 锁定截止时间
     * @return 是否已过期
     */
    private boolean isLockExpired(Date lockEndTime) {
        if (lockEndTime == null) {
            return false; // 没有截止时间的锁定认为未过期
        }
        return new Date().after(lockEndTime);
    }
    
    /**
     * 创建锁定记录到MongoDB
     * @param lockId 锁定主表ID
     * @param lockDTO 锁定参数
     * @param creditCode 企业统一社会信用代码
     * @param opportunityType 机会类型
     * @return 记录ID
     */
    private String createLockRecord(Integer lockId, OpportunityLockDTO lockDTO, String creditCode, String opportunityType) {
        try {
            EnterpriseOppLockRecordAddDTO recordDTO = new EnterpriseOppLockRecordAddDTO();
            recordDTO.setLockId(lockId);
            recordDTO.setCreditCode(creditCode);
            recordDTO.setOpportunityType(opportunityType);
            recordDTO.setLockUser("system"); // 临时设置，实际应该从上下文获取
            recordDTO.setLockTime(new Date());
            recordDTO.setLockEndTime(lockDTO.getLockEndTime());
            
            // 转换文件信息
            if (lockDTO.getLockFiles() != null && !lockDTO.getLockFiles().isEmpty()) {
                List<EnterpriseOppLockRecordAddDTO.LockFileInfo> lockFiles = new ArrayList<>();
                for (OpportunityLockDTO.LockFileInfo sourceFile : lockDTO.getLockFiles()) {
                    EnterpriseOppLockRecordAddDTO.LockFileInfo targetFile = new EnterpriseOppLockRecordAddDTO.LockFileInfo();
                    targetFile.setFileName(sourceFile.getFileName());
                    targetFile.setFileUrl(sourceFile.getFileUrl());
                    targetFile.setFileSize(sourceFile.getFileSize());
                    targetFile.setFileType(sourceFile.getFileType());
                    targetFile.setUploadTime(sourceFile.getUploadTime() != null ? sourceFile.getUploadTime() : new Date());
                    lockFiles.add(targetFile);
                }
                recordDTO.setLockFiles(lockFiles);
            }
            
            Result<String> result = enterpriseOppLockRecordService.add(recordDTO);
            if ("SUCCESS".equals(result.getResp_code())) {
                return result.getDatas();
            } else {
                logger.error("创建锁定记录失败：{}", result.getResp_msg());
                return null;
            }
        } catch (Exception e) {
            logger.error("创建锁定记录失败", e);
            return null;
        }
    }
    
    /**
     * 更新机会的锁定状态
     * @param opportunityId 机会ID
     * @param lockStatus 锁定状态
     */
    private void updateOpportunityLockStatus(Integer opportunityId, Integer lockStatus) {
        try {
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity != null) {
                opportunity.setLockStatus(lockStatus);
                opportunity.setUpdateTime(new Date());
                opportunity.setUpdateId("system"); // 临时设置，实际应该从上下文获取
                opportunityService.updateByPrimaryKey(opportunity);
                logger.info("更新机会锁定状态成功，机会ID：{}，锁定状态：{}", opportunityId, lockStatus);
            }
        } catch (Exception e) {
            logger.error("更新机会锁定状态失败，机会ID：{}，锁定状态：{}", opportunityId, lockStatus, e);
        }
    }
    
    /**
     * 更新其他机会的锁定状态
     * @param opportunityType 机会类型
     * @param creditCode 企业信用代码
     * @param currentOpportunityId 当前机会ID（排除）
     * @param lockStatus 锁定状态
     */
    private void updateOtherOpportunitiesLockStatus(String opportunityType, String creditCode, Integer currentOpportunityId, Integer lockStatus) {
        try {
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            
            // 1. 根据机会类型和企业信用代码查询其他进行中机会
            List<Opportunity> otherOpportunities = opportunityService.selectActiveOpportunitiesByTypeAndCreditCode(
                opportunityType, creditCode, currentOpportunityId);
            
            if (otherOpportunities == null || otherOpportunities.isEmpty()) {
                logger.info("没有找到需要更新锁定状态的其他机会，机会类型：{}，企业信用代码：{}，排除机会ID：{}", 
                           opportunityType, creditCode, currentOpportunityId);
                return;
            }
            
            // 2. 提取机会ID列表
            List<Integer> opportunityIds = otherOpportunities.stream()
                .map(Opportunity::getId)
                .collect(Collectors.toList());
            
            // 3. 批量更新锁定状态
            int updateCount = opportunityService.batchUpdateLockStatus(opportunityIds, lockStatus, currentUserId);
            
            logger.info("批量更新其他机会锁定状态完成，机会类型：{}，企业信用代码：{}，排除机会ID：{}，目标状态：{}，更新数量：{}", 
                       opportunityType, creditCode, currentOpportunityId, lockStatus, updateCount);
            
            // 5. 记录详细的更新信息（用于调试和审计）
            if (logger.isDebugEnabled()) {
                String updatedOpportunityIds = opportunityIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
                logger.debug("更新的机会ID列表：[{}]", updatedOpportunityIds);
            }
            
        } catch (Exception e) {
            logger.error("更新其他机会锁定状态失败，机会类型：{}，企业信用代码：{}，排除机会ID：{}，目标状态：{}", 
                        opportunityType, creditCode, currentOpportunityId, lockStatus, e);
            // 这里不抛出异常，避免影响主要的锁定流程
        }
    }
    
    /**
     * 解锁机会
     * @param opportunityId 机会ID
     * @return 解锁结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> unlockOpportunity(Integer opportunityId) {
        try {
            if (opportunityId == null) {
                return Result.failed("机会ID不能为空");
            }
            
            String currentUserId = "system"; // 临时设置，实际应该从上下文获取
            
            // 1. 查询机会信息
            Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
            if (opportunity == null) {
                return Result.failed("机会不存在");
            }
            
            // 2. 查询关联企业信息
            GenAgentEnterprise enterprise = null;
            String creditCode = null;
            
            if (opportunity.getAgentEnterpriseId() != null) {
                enterprise = genAgentEnterpriseService.selectByPrimaryKey(opportunity.getAgentEnterpriseId());
                if (enterprise != null) {
                    creditCode = enterprise.getCreditCode();
                }
            }
            
            if (creditCode == null || creditCode.trim().isEmpty()) {
                return Result.failed("机会关联的企业信息不完整，无法进行解锁");
            }
            
            // 3. 查询锁定记录
            EnterpriseOppLockVO lockRecord = this.mapper.selectByOpportunityIdAndCreditCode(opportunityId, creditCode);
            if (lockRecord == null) {
                return Result.failed("该机会未被锁定");
            }
            
            // 4. 删除锁定记录（逻辑删除）
            int deleteResult = this.mapper.deleteLogical(lockRecord.getId(), currentUserId);
            if (deleteResult <= 0) {
                return Result.failed("删除锁定记录失败");
            }
            
            // 5. 更新机会锁定状态为已解锁
            updateOpportunityLockStatus(opportunityId, 3); // 3-已解锁
            
            // 6. 将MongoDB中对应记录状态设置为无效
            if (lockRecord.getCurrentRecordId() != null && !lockRecord.getCurrentRecordId().trim().isEmpty()) {
                Result<Boolean> invalidateResult = enterpriseOppLockRecordService.invalidateByLockId(lockRecord.getId());
                if (!"SUCCESS".equals(invalidateResult.getResp_code())) {
                    logger.warn("设置MongoDB记录为无效失败：{}", invalidateResult.getResp_msg());
                }
            }
            
            logger.info("机会解锁成功，机会ID：{}，锁定记录ID：{}", opportunityId, lockRecord.getId());
            return Result.succeed("机会解锁成功");
            
        } catch (Exception e) {
            logger.error("解锁机会失败", e);
            return Result.failed("解锁机会失败：" + e.getMessage());
        }
    }

    public boolean hasLockOpp(String creditCode, String opportunityType) {
        return mapper.hasLockOpp(creditCode, opportunityType) > 0;
    }
}
