package com.kbao.kbcelms.opportunityteamdivision.service;


import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.adapter.ParamClientAdapter;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.divisionratio.model.DivisionRatio;
import com.kbao.kbcelms.divisionratio.service.DivisionRatioService;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService;
import com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO;
import com.kbao.kbcelms.opportunityteamdivision.vo.ConfirmDivisionVo;
import com.kbao.kbcelms.opportunityteamdivision.vo.OpportunityTeamDivisionQueryVo;import com.kbao.kbcelms.sms.AppSmsWebService;
import com.kbao.kbcelms.sms.MailRequest;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision;
import com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

/**
 * 机会项目分工
 */
@Slf4j
@Service
public class OpportunityTeamDivisionService extends BaseSQLServiceImpl<OpportunityTeamDivision, Integer,OpportunityTeamDivisionMapper> {

    private static final String DIVISION_CONFIG_KEY = "elms.division.default.config";

    // 仅查询自己的分工权限码
    private static final String DIVISION_SELF = "elms:opportunity:member:divide:view:self";
    // 查询所有的分工权限码
    private static final String DIVISION_ALL = "elms:opportunity:member:divide:view:all";


    @Autowired
    private DivisionRatioService divisionRatioService;
    @Autowired
    private AppSmsWebService appSmsWebService;
    @Autowired
    private OpportunityService opportunityService;
    @Autowired
    private OpportunityTeamService opportunityTeamService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 发送确认比例邮件
     */
    public void sendNotice(OpportunityTeamDivisionVO param,String tenantId,String manager,String userId){
        if(param.getOpportunityId() == null){
            throw new BusinessException("机会id不能为空");
        }
        // 发送邮件通知
        Opportunity opportunity= opportunityService.selectByPrimaryKey(param.getOpportunityId());
        if(opportunity == null){
            throw new BusinessException("未找到机会项目");
        }


        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, param.getOpportunityId(), param.getNum(),null,null,null);
        if(EmptyUtils.isEmpty(list)){
            return;
        }

        List<OpportunityTeamDivision> divisionList = new ArrayList<>();
        for(OpportunityTeamDivisionVO vo: list){
            OpportunityTeamDivision division = new OpportunityTeamDivision();
            division.setId(vo.getId());
            Integer times = vo.getTimes()==null?1:vo.getTimes()+1;
            division.setTimes(times);
            division.setUpdateId(userId);
            division.setUpdateTime(new Date());
            divisionList.add(division);

            // 默认确认的不发邮件
            if(vo.getIsDefault() ==1){
                continue;
            }

            DivisionRatio divisionRatio = divisionRatioService.findById(String.valueOf(vo.getDivisionId()));
            this.sendMail(tenantId,vo.getEmail(),vo.getNickName(),opportunity.getOpportunityName(),manager,divisionRatio.getName(),String.valueOf(vo.getDivisionRatio().intValue()) );



        }
        this.batchUpdate(divisionList);
    }

    /**
     * 发送确认邀请邮件
     *
     * @param tenantId
     * @param email 成员邮箱
     * @param name 成员姓名
     * @param oppName 项目名
     * @param manager 项目经理名
     */
    private void sendMail(String tenantId,String email,String name,String oppName,String manager,String divisionName,String ratio){

        String content = buildMsg(name,oppName,divisionName,ratio,manager);
        MailRequest mailRequest = MailRequest.builder()
                .tenantId(tenantId)
                .appCode("userWeb")
                .title("机会项目分工比例确认")
                .mailTo(email)
                .content(content)
                .build();

        appSmsWebService.sendMail(mailRequest);
    }

    private String buildMsg(String name,String oppName,String divisionName,String ratio,String manager){
        return name+"您好，"+oppName+"项目组根据沟通，您在项目组中负责"+divisionName+"，分配比例是"+ratio+"%，请确认是否接受？望回复，如有疑问可直接与项目经理"+manager+"联系，感谢!";
    }

    /**
     *  接受或拒绝邀请
     *
     * @param id
     * @param status
     * @param updateId
     */
    public void acceptOrReject(Integer id,Integer status,String updateId)  {
        OpportunityTeamDivision opportunityTeamDivision = new OpportunityTeamDivision();
        opportunityTeamDivision.setId(id);
        opportunityTeamDivision.setUpdateTime(new Date());
        opportunityTeamDivision.setUpdateId(updateId);
        opportunityTeamDivision.setConfirmTime(new Date());
        opportunityTeamDivision.setConfirmId(updateId);
        // 参与状态 0 待确认，1已确认  5 已拒绝
        opportunityTeamDivision.setStatus(status);

        this.mapper.updateByPrimaryKeySelective(opportunityTeamDivision);
    }



    /**
     * 复制到项目二次分工
     */
    public void copy2Second(Integer opportunityId,String tenantId){
        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }

        // 判断是否已经生成项目二次分工
        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, opportunityId, 2,null,null,null);
        if(EmptyUtils.isNotEmpty(list)){
            log.error("项目二次分工已生成，不能重复生成:"+opportunityId);
            return ;
        }
        // 那默认成员角色配置
//        List<OpportunityTeamDivisionVO> configList = getDivisionConfigList();
//        List<String> divisionIds = configList.stream().distinct().map(OpportunityTeamDivisionVO::getDivisionId).collect(Collectors.toList());

        // 首次分工数据
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("opportunityId", opportunityId);
        map.put("tenantId", tenantId);
        map.put("num",1);
        List<OpportunityTeamDivision> first  = this.mapper.selectAll(map);
        for(OpportunityTeamDivision otd: first){
            otd.setNum(2);
            if(otd.getIsDefault()!=1){
                otd.setStatus(0);
            } else {
                otd.setStatus(1);
            }
        }

        this.batchInsert(first);
    }

    /**
     * 初始化项目分工
     *
     * @param opportunityId
     * @param members
     * @param tenantId
     */
    public void init(Integer opportunityId,List<OpportunityTeamMember> members,String tenantId){
        log.info("OpportunityTeamDivisionService init ");
        if(EmptyUtils.isEmpty(members)){
            return;
        }

        Map<Integer,OpportunityTeamDivisionVO> configMap = getDivisionConfig();

        List<DivisionRatio> divisionRatioList = this.divisionRatioService.getDivisionRatioList();
        Map<String, DivisionRatio> map = null;
        if(EmptyUtils.isNotEmpty(divisionRatioList)) {
            map = divisionRatioList.stream().collect(Collectors.toMap(DivisionRatio::getId, v -> v));
        }

        List<OpportunityTeamDivision> divisions = new ArrayList<>();
        for(OpportunityTeamMember member:  members){
            OpportunityTeamDivision division = new OpportunityTeamDivision();
            // 分工处理
            OpportunityTeamDivisionVO config = configMap.get(member.getRoleType());
            if(config == null){
                throw new BusinessException("未找到默认分工配置 >>"+member.getRoleType());
            }
            division.setDivisionId(config.getDivisionId());
            if(map!=null){
                DivisionRatio dr = map.get(config.getDivisionId());
                if(dr != null){
                    division.setDivisionRatio(new BigDecimal(dr.getRatio()));
                }
            }
            division.setIsDefault(1);
            division.setStatus(1);
            division.setNum(1);
            division.setTenantId(tenantId);
            division.setIsDeleted(0);
            division.setUserId(member.getUserId());
            division.setTimes(0);
            division.setOpportunityId(member.getOpportunityId());
            division.setCreateId("SYSTEM");
            division.setCreateTime(new Date());
            divisions.add(division);
        }
        // 清理老的分工
        this.deleteByOpportunityId(opportunityId);

        this.batchInsert(divisions);
    }

    /**
     * 获取默认分工配置
     */
    private Map<Integer,OpportunityTeamDivisionVO> getDivisionConfig(){
        /**
         * 字段例子：
         * [{
         * 	"roleType":3,
         * 	"divisionId":"6889c3ba5a697f16f3693796",
         * 	"ratio":20
         * }]
         */
        String config =(String) redisUtil.get(DIVISION_CONFIG_KEY);
        if(EmptyUtils.isNotEmpty(config)){
            log.info("getDivisionConfig {}",config);
            List<OpportunityTeamDivisionVO> divisionList = JSON.parseArray(config,OpportunityTeamDivisionVO.class);
            if(EmptyUtils.isNotEmpty(divisionList)){
                Map<Integer,OpportunityTeamDivisionVO>  map = divisionList.stream().collect(Collectors.toMap(OpportunityTeamDivisionVO::getRoleType, v -> v));
                return map;
            }
        }
        return null;
    }

    private List<OpportunityTeamDivisionVO> getDivisionConfigList(){
        String config =(String) redisUtil.get(DIVISION_CONFIG_KEY);
        if(EmptyUtils.isNotEmpty(config)){
            log.info("getDivisionConfig {}",config);
            List<OpportunityTeamDivisionVO> divisionList = JSON.parseArray(config,OpportunityTeamDivisionVO.class);
            return divisionList;
        }
        return null;
    }

    /**
     * 设置默认分工配置
     *
     * @param config
     */
    public void setDivisionConfig(String config){
        redisUtil.set(DIVISION_CONFIG_KEY, config);
    }

    /**
     * 新增
     */
    public void add(OpportunityTeamDivisionVO vo,String createId,String tenantId) {

        if(vo.getOpportunityId() == null){
            throw new BusinessException("机会id不能为空");
        }

        OpportunityTeam opportunityTeam = opportunityTeamService.findMemeberByUserId(vo.getUserId(),vo.getOpportunityId());
        if(opportunityTeam == null){
            throw new BusinessException("未找到该项目成员");
        }

        // 判断分工是否重复添加
        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, vo.getOpportunityId(), vo.getNum(),vo.getDivisionId(),vo.getUserId(),null);
        if(EmptyUtils.isNotEmpty(list)){
            throw new BusinessException("该成员已存在相同的分工，请勿重复添加");
        }

        if(StringUtil.isNotEmpty(vo.getRemark())){
            vo.setRemark(vo.getRemark().trim());
        }

        // 分工上限比例校验
        checkRatio(vo,tenantId);

        OpportunityTeamDivision division = new OpportunityTeamDivision();
        // 是否默认用户，分工次数由前端传入
        BeanUtils.copyProperties(vo, division);
        // 只有初始化时才是默认分工，新增的一律不是默认分工
        division.setIsDefault(0);
        division.setStatus(0);
        division.setCreateId(createId);
        division.setCreateTime(new Date());
        division.setIsDeleted(0);


        this.mapper.insert(division);
    }

    private void checkRatio(OpportunityTeamDivisionVO vo,String tenantId) {
        // 分工上限比例校验
        DivisionRatio divisionRatio = divisionRatioService.findById(vo.getDivisionId());
        if(divisionRatio == null){

            throw new BusinessException("分工配置未找到 >>"+vo.getDivisionId());
        }

        // 查找已添加的所有分工,修改会排除要修改的分工记录
        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, vo.getOpportunityId(), vo.getNum(),vo.getDivisionId(),null,vo.getId());
        if(EmptyUtils.isNotEmpty(list)){
            if((list.size()+1)>divisionRatio.getNumber()){
                throw new BusinessException("超过分工人数上限"+divisionRatio.getNumber());
            }

            BigDecimal total = BigDecimal.ZERO;
            for(OpportunityTeamDivisionVO exist : list){
                total = total.add(exist.getDivisionRatio());
            }
            total =  total.add(vo.getDivisionRatio());

            if(total.compareTo(new BigDecimal(divisionRatio.getRatio())) > 0){
                throw new BusinessException("超过分工比例上限"+divisionRatio.getRatio());
            }
        }
    }

    /**
     * 修改分工比例
     *
     * @param vo
     * @param updateId
     * @param tenantId
     */
    public void update(OpportunityTeamDivisionVO vo,String updateId,String tenantId){
        if(vo.getId() == null){
            throw new BusinessException("分工id不能为空");
        }

        if(vo.getOpportunityId() == null){
            throw new BusinessException("机会id不能为空");
        }

        // 判断分工是否重复添加
        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, vo.getOpportunityId(), vo.getNum(),vo.getDivisionId(),vo.getUserId(),vo.getId());
        if(EmptyUtils.isNotEmpty(list)){
            throw new BusinessException("该成员已存在相同的分工，请勿重复添加");
        }


        // 分工上限比例校验
        checkRatio(vo,tenantId);

        OpportunityTeamDivision division = new OpportunityTeamDivision();
        division.setId(vo.getId());
        division.setDivisionId(vo.getDivisionId());
        division.setUserId(vo.getUserId());
        division.setIsDefault(vo.getIsDefault());
        division.setDivisionRatio(vo.getDivisionRatio());
        division.setRemark(vo.getRemark());
        division.setUpdateId(updateId);

        this.mapper.updateByPrimaryKeySelective(division);
    }

    public List<OpportunityTeamDivisionVO> findDivisionByAuth(String tenantId, Integer opportunityId, Integer num,String curUserId){

        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }
        // 默认查首次分工
        if(num == null){
            num = 1;
        }

        // 项目经理能看所有分工，非项目经理只能看自己的分工
//        Boolean isManager = true;
        // 当前用户id传空时，不判断是否项目经理，默认查所有数据
//        if(StringUtil.isNotEmpty(curUserId)){
//            isManager = this.opportunityTeamService.checkIsManager(opportunityId,curUserId);
//        }

        // 根据权限位判断分工查询权限
        UserRoleAuthVO userRoleAuthVO =  this.userService.getUserRoleAuthByUserId(curUserId,tenantId);
        if(userRoleAuthVO == null){
            throw new BusinessException("用户没有查询项目分工的权限");
        }
        // 是否有查询自己分工的权限
        Boolean hasSelf = userRoleAuthVO.getRoleAuthStr().contains(DIVISION_SELF);
        // 是否有查询所有分工的权限
        Boolean hasAll = userRoleAuthVO.getRoleAuthStr().contains(DIVISION_ALL);

        if(!hasSelf && !hasAll){
            // 一个权限没有返回空
            return null;
        }

        return this.findDivision(tenantId,opportunityId,num,curUserId,hasAll);
    }

    public List<OpportunityTeamDivisionVO> findDivision(String tenantId, Integer opportunityId, Integer num,String curUserId){
        return this.findDivision(tenantId,opportunityId,num,curUserId,true);
    }

    /**
     * 查找项目分工
     *
     * @param tenantId
     * @param opportunityId
     * @param num
     * @return
     */
    public List<OpportunityTeamDivisionVO> findDivision(String tenantId, Integer opportunityId, Integer num,String curUserId,boolean hasAll){

        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }
        // 默认查首次分工
        if(num == null){
            num = 1;
        }


        List<OpportunityTeamDivisionVO> list  = this.mapper.findDivision(tenantId, opportunityId, num,null,hasAll?null:curUserId,null);
        if(EmptyUtils.isNotEmpty(list)){
            // 处理分工名称和分工比例上限
            List<DivisionRatio> divisionRatioList = this.divisionRatioService.getDivisionRatioList();
            if(EmptyUtils.isNotEmpty(divisionRatioList)){
                Map<String,DivisionRatio> map = divisionRatioList.stream().collect(Collectors.toMap(DivisionRatio::getId, v -> v));
                for(OpportunityTeamDivisionVO vo: list){
                    DivisionRatio dr = map.get(vo.getDivisionId());
                    if(dr != null){
                        vo.setRatio(dr.getRatio());
                        vo.setName(dr.getName());
                    }
                }

            }
        }

        return list;
    }

    /**
     * 查询项目分工详情
     *
     * @param tenantId
     * @param id
     * @return
     */
    public OpportunityTeamDivisionVO findOneDivision(String tenantId, Integer id){
        OpportunityTeamDivisionVO vo =  this.mapper.findOneDivision(tenantId, id);

        DivisionRatio divisionRatio = divisionRatioService.findById(String.valueOf(vo.getDivisionId()));
        if(divisionRatio != null){
            vo.setName(divisionRatio.getName());
            vo.setRatio(divisionRatio.getRatio());
        }
        return vo;
    }

    /**
     * 根据机会id删除所有项目分工比例
     *
     * @param opportunityId
     */
    public void deleteByOpportunityId(Integer opportunityId){
        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }

        this.mapper.deleteByOpportunityId(opportunityId,null);
    }

    /**
     * 根据机会id与用户id删除所有项目分工比例
     *
     * @param opportunityId
     * @param userId
     */
    public void deleteByOpportunityIdAndUserId(Integer opportunityId,String userId){
        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }

        this.mapper.deleteByOpportunityId(opportunityId,userId);
    }

    /**
     * 获取项目分工信息并格式化为指定结构
     * 格式：陈国荣/机会提供/20%；徐晨旭/签订独家授权/20%；王年金/项目管理/30%；李增额/方案设计与排分/15%
     *
     * @param opportunityId 机会ID
     * @param tenantId 租户ID
     * @param num 分工版本号（1-首次分工，2-二次确认分工）
     * @return 格式化后的分工信息字符串
     */
    public String getFormattedDivisionInfo(Integer opportunityId, String tenantId, Integer num) {
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(tenantId)) {
            throw new BusinessException("租户ID不能为空");
        }
        if (num == null) {
            num = 1; // 默认查首次分工
        }

        // 获取项目分工信息
        List<OpportunityTeamDivisionVO> divisionList = findDivision(tenantId, opportunityId, num,null);
        if (EmptyUtils.isEmpty(divisionList)) {
            return "";
        }

        // 格式化分工信息
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < divisionList.size(); i++) {
            OpportunityTeamDivisionVO division = divisionList.get(i);
            
            // 获取用户信息
            String userName = division.getNickName();
            
            // 获取分工名称
            String divisionName = "";
            
            divisionName = division.getName();
            
            // 获取分工比例
            String divisionRatio = "";
            if (division.getDivisionRatio() != null) {
                divisionRatio = division.getDivisionRatio().toString() + "%";
            }
            
            // 拼接格式：用户名/分工名称/比例%
            result.append(userName).append("/").append(divisionName).append("/").append(divisionRatio);
            
            // 添加分隔符（最后一个不添加）
            if (i < divisionList.size() - 1) {
                result.append("；\n");
            }
        }
        return result.toString();
    }

    // 根据机会会userId更新分工userId，不区分1次还是2次
    public void updateDivisionUserId(String newUserId,String oldUserId,Integer opportunityId,String updateId){
        this.mapper.updateUserId(opportunityId,oldUserId,newUserId,updateId);
    }

    /**
     * 根据机会ID更新租户ID
     * @param opportunityId 机会ID
     * @param newTenantId 新的租户ID
     */
    public void updateTenantIdByOpportunityId(Integer opportunityId, String newTenantId) {
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(newTenantId)) {
            throw new BusinessException("新租户ID不能为空");
        }
        
        // 查询该机会的所有分工记录
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        List<OpportunityTeamDivision> divisions = this.selectByParam(param);
        
        // 批量更新租户ID
        for (OpportunityTeamDivision division : divisions) {
            division.setTenantId(newTenantId);
            division.setUpdateId(ElmsContext.getUser().getUserId());
            division.setUpdateTime(new Date());
            this.updateByPrimaryKey(division);
        }
    }

    public Map<String, Object> getDivisionByUserId(Integer opportunityId, String userId) {
        Map<String, Object> divisionMap = new HashMap<>();
        List<OpportunityTeamDivisionQueryVo> list = mapper.getDivisionByUserId(opportunityId, userId);
        if (EmptyUtils.isEmpty(list)) {
            return divisionMap;
        }
        // 处理分工名称和分工比例上限
        List<DivisionRatio> divisionRatioList = this.divisionRatioService.getDivisionRatioList();
        Map<String,DivisionRatio> map = new HashMap<>();
        if(EmptyUtils.isNotEmpty(divisionRatioList)){
            map = divisionRatioList.stream().collect(Collectors.toMap(DivisionRatio::getId, v -> v));
        }
        for(OpportunityTeamDivisionQueryVo vo: list){
            DivisionRatio dr = map.get(vo.getDivisionId());
            if(dr != null){
                vo.setName(dr.getName());
            }
        }
        Map<String,List<OpportunityTeamDivisionQueryVo>> listMap = list.stream().filter(a -> a.getNum().equals(1) || a.getNum().equals(2))
            .collect(Collectors.groupingBy(a -> a.getNum().equals(1) ? "first":"second"));
        listMap.forEach((k,v) -> {
            Map<String,Object> data = new HashMap<>();
            data.put("list",v);
            String status = v.stream().anyMatch(a -> a.getStatus().equals("0")) ? "0" : "1";
            data.put("status",status);
            divisionMap.put(k,data);
        });
        return divisionMap;
    }

    public void confirmRatio(ConfirmDivisionVo vo) {
        List<Integer> ids = vo.getIds();
        if (EmptyUtils.isEmpty(ids)) {
            return;
        }
        mapper.confirmDivision(ids);
    }

    public Integer getNeedConfirmNum(String agentCode) {
        return mapper.getNeedConfirmNum(agentCode);
    }
}
