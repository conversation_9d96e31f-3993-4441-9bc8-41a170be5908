package com.kbao.kbcelms.opportunityteamdivision.dao;

import java.util.*;
import java.util.Map;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision;
import com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO;
import com.kbao.kbcelms.opportunityteamdivision.vo.OpportunityTeamDivisionQueryVo;import org.apache.ibatis.annotations.Param;

public interface OpportunityTeamDivisionMapper  extends BaseMapper<OpportunityTeamDivision, Integer>{

    /**
     * 查找项目分工
     *
     * @param tenantId
     * @param opportunityId
     * @return
     */
    List<OpportunityTeamDivisionVO> findDivision(@Param("tenantId") String tenantId, @Param("opportunityId") Integer opportunityId,@Param("num") Integer num,@Param("divisionId") String divisionId,@Param("userId") String userId,@Param("excludeId") Integer excludeId);

    /**
     * 查找单个项目分工
     *
     * @param tenantId
     * @param id
     * @return
     */
    OpportunityTeamDivisionVO findOneDivision(@Param("tenantId") String tenantId,@Param("id") Integer id);


    /**
     * 根据机会id删除项目成员分工比例
     *
     * @param opportunityId
     */
    void deleteByOpportunityId(@Param("opportunityId") Integer opportunityId,@Param("userId") String userId);

    /**
     * 根据机会id删除项目成员分工比例
     *
     */
    void updateUserId(@Param("opportunityId") Integer opportunityId,@Param("oldUserId") String oldUserId,@Param("userId") String userId,@Param("updateId") String updateId);

    List<OpportunityTeamDivisionQueryVo> getDivisionByUserId(@Param("opportunityId") Integer opportunityId, @Param("userId") String userId);

    void confirmDivision(@Param("ids") List<Integer> ids);

    int getNeedConfirmNum(@Param("userId") String userId);
}