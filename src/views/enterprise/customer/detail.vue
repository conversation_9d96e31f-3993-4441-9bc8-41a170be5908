<template>
  <div class="enterprise-customer-detail-container">
    <!-- 顾问信息头部 -->
    <div class="agent-info-header">
      <div class="info-item">
        <span class="label">顾问姓名：</span>
        <span class="value">{{ agentName }}</span>
      </div>
      <div class="info-item">
        <span class="label">工号：</span>
        <span class="value">{{ agentCode }}</span>
      </div>
      <div class="info-item">
        <span class="label">机构：</span>
        <span class="value">{{ legalName }}</span>
      </div>
      <div class="info-item">
        <span class="label">营业部：</span>
        <span class="value">{{ salesCenterName }}</span>
      </div>
    </div>

    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业列表"
      subtitle="顾问关联的企业信息列表"
      title-icon="el-icon-office-building"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :breadcrumb-items="breadcrumbItems"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无企业数据"
      empty-description="暂无符合条件的企业信息"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable'
import { getAgentEnterpriseList } from '@/api/agentEnterprise'
import { getEnterpriseTypeEnum } from '@/api/enterprise/type'

export default {
  name: 'EnterpriseCustomerDetail',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          agentCode: this.$route.params.agentCode,
          name: '',
          dtType: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      enterpriseTypeOptions: [],

      // 顾问信息
      agentCode: '',
      agentName: '',
      legalName: '',
      salesCenterName: '',

      // 面包屑配置
      breadcrumbItems: [
        { label: '企业客户管理', path: '/enterprise/customer' },
        { label: '企业详情', path: '' }
      ],

      // 搜索表单配置
      searchFormConfig: [
        {
          label: '企业名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入企业名称'
        },
        {
          label: '企业类型',
          name: 'dtType',
          type: 'select',
          placeholder: '请选择企业类型',
          list: [

          ]
        },
         {
           label: '是否验真',
           name: 'isVerified',
           type: 'select',
           placeholder: '请选择',
           list: [
             { label: '是', value: 1 },
             { label: '否', value: 0 }
           ]
         }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '企业名称',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'creditCode',
          label: '社会统一信用代码',
          width: 180,
          align: 'center'
        },
        {
          prop: 'categoryName',
          label: '所属行业',
          width: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'city',
          label: '所在地',
          width: 120,
          align: 'center'
        },
        {
          prop: 'staffScale',
          label: '人员规模',
          width: 120,
          align: 'center'
        },
        {
          prop: 'annualIncome',
          label: '营业收入',
          width: 120,
          align: 'center'
        },
        {
          prop: 'dtType',
          label: '企业分类',
          width: 120,
          align: 'center'
        },
        {
          prop: 'isVerified',
          label: '是否验真',
          width: 100,
          align: 'center'
        },
        {
          prop: 'opportunityCount',
          label: '线索条数',
          width: 100,
          align: 'center'
        }
      ]
    }
  },

  mounted() {
    console.log('Detail页面mounted，初始searchFormConfig:', this.searchFormConfig)
    this.initAgentInfo()
    this.loadEnterpriseTypes()
    this.loadData()
  },

  methods: {
    // 初始化顾问信息
    initAgentInfo() {
      this.agentCode = this.$route.params.agentCode || ''
      this.agentName = this.$route.params.agentName || ''
      this.legalName = this.$route.params.legalName || ''
      this.salesCenterName = this.$route.params.salesCenterName || ''
    },

    // 加载企业类型选项
    async loadEnterpriseTypes() {
      try {
        console.log('开始加载企业类型选项...')
        const response = await getEnterpriseTypeEnum()
        console.log('企业类型API响应:', response)

        // getEnterpriseTypeEnum接口返回格式：{code: 200, msg: "成功", data: [{name: "名称", code: "编码"}]}
        let options = []
        console.log('检查response结构:', {
          hasResponse: !!response,
          hasData: !!(response && response.data),
          isDataArray: !!(response && response.data && Array.isArray(response.data)),
          responseKeys: response ? Object.keys(response) : 'no response',
          dataContent: response && response.data ? response.data : 'no data'
        })

        if (response && response.data && Array.isArray(response.data)) {
          options = response.data
        }

        if (options && options.length > 0) {
          this.enterpriseTypeOptions = options
          // 将getEnterpriseTypeEnum返回的数据格式转换为下拉选项格式
          const mappedOptions = options.map(item => ({
            label: item.name,
            value: item.code
          }))
          console.log('映射后的企业类型选项:', mappedOptions)

          // 使用Vue.set确保响应式更新
          this.$set(this.searchFormConfig[1], 'list', mappedOptions)
          console.log('更新后的searchFormConfig:', this.searchFormConfig)

          // 强制更新组件
          this.$forceUpdate()
        } else {
          console.log('企业类型API返回空数据或格式不正确')
        }
      } catch (error) {
        console.error('加载企业类型失败:', error)
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }
        console.log('this.searchForm:', this.searchForm)
        const response = await getAgentEnterpriseList(params)
        if (response && response.list) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载企业列表数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchForm) {
      this.searchForm = { ...searchForm }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        param: {
          agentCode: this.$route.params.agentCode,
          name: '',
          dtType: ''
        }
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-customer-detail-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .agent-info-header {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 30px;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        font-weight: 600;
        color: #333;
        margin-right: 8px;
      }

      .value {
        color: #666;
        font-size: 14px;
      }
    }
  }
}
</style>
