<template>
  <div class="member-manage">
    <TableToolTemp :toolListProps="toolListProps" class="log-tool" @handleTool="manageHandleTool"></TableToolTemp>
    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55px"> </el-table-column>

      <el-table-column align="center" prop="opportunityName" label="机会名称" width="120px"></el-table-column>
      <el-table-column align="center" prop="bizCcode" label="机会编码"></el-table-column>
      <el-table-column align="center" prop="projectManagerName" label="项目经理"></el-table-column>

      <el-table-column align="center" prop="agentName" label="服务顾问"></el-table-column>
      <el-table-column align="center" prop="companyName" label="所属机构" width="200px"></el-table-column>
      <el-table-column align="center" prop="salesCenterName" label="所属营业部" width="200px"></el-table-column>
      <el-table-column align="center" prop="enterpriseName" label="企业名称" width="220px"></el-table-column>
      <el-table-column align="center" prop="generalInsuranceType" label="客户需求"></el-table-column>
      <el-table-column align="center" prop="opportunityType" label="业务渠道">
        <template slot-scope="scope">
          <div>{{ opportunityTypes | getOpportunityTypeName(scope.row.opportunityType) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="insureNum" label="预估投保人数" width="220px"></el-table-column>
      <el-table-column align="center" prop="premiumBudget" label="保费预算"></el-table-column>
      <el-table-column align="center" prop="isBid" label="是否需要投标" width="120px">
        <template slot-scope="scope">
          {{scope.row.isBid|getDicItemName("gen.yesorno.num")}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="submitTime" label="提交时间"  width="200px"></el-table-column>
      <el-table-column align="center" prop="status" label="机会状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">
            {{scope.row.processStep}}
          </span>
          <span v-else-if="scope.row.status == 4">
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }} - {{ closeReasonTypes | getCloseReasonDesc(scope.row.closeReasonType) }}
          </span>
          <span v-else>
            {{ scope.row.status | getDicItemName("elms.opportunity.status") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150px">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="detail(scope.row)">机会详情</el-button>
          <el-button class="btn-center" type="text" @click="changeManager(scope.row)" >变更项目管理人</el-button>

        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <DtPopup :isShow.sync="showChangePopup" @close="closeChangePopup" title="变更项目管理人" center :footer="false"
             width="900px">
      <div class="popup-content">
        <!-- 机会信息 -->
        <div class="restart-close-tip">
          <p style="margin-bottom: 10px"  v-if="this.selopportunity != null">
            机会名称：{{this.selopportunity.opportunityName}} &nbsp;&nbsp;&nbsp;&nbsp;
            机会ID：{{this.selopportunity.bizCode}} &nbsp;&nbsp;&nbsp;&nbsp;
            机会状态：
              <span v-if="this.selopportunity.status == 1">
              {{this.selopportunity.processStep}}
            </span>
              <span v-else-if="this.selopportunity.status == 4">
              {{ this.selopportunity.status | getDicItemName("elms.opportunity.status") }} - {{ closeReasonTypes | getCloseReasonDesc(this.selopportunity.closeReasonType) }}
            </span>
              <span v-else>
              {{ this.selopportunity.status | getDicItemName("elms.opportunity.status") }}
            </span>
          </p>
          <div class="table-container">
            <el-table :data="selOpportunitys"  style="width: 100%" v-hover v-if="selOpportunitys!=null && selOpportunitys.length>0">
              <el-table-column align="center" prop="opportunityName" label="机会名称"></el-table-column>
              <el-table-column align="center" prop="bizCode" label="机会ID"></el-table-column>
              <el-table-column align="center" prop="status" label="机会状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">
                    {{scope.row.processStep}}
                  </span>
                          <span v-else-if="scope.row.status == 4">
                    {{ scope.row.status | getDicItemName("elms.opportunity.status") }} - {{ closeReasonTypes | getCloseReasonDesc(scope.row.closeReasonType) }}
                  </span>
                          <span v-else>
                    {{ scope.row.status | getDicItemName("elms.opportunity.status") }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 人员搜索 -->
          <SearchForm  @changeSelect="handleChangeSearch" :searchForm="changeSearchForm" :showSearch="false" :searchFormTemp="changeSearchFormTemp" />
          <div class="table-container">
            <el-table :data="changeUserList" style="width: 100%;" @current-change="handleChangeUserSelect">
              <el-table-column label="" width="50" align="center">
                <template slot-scope="scope">
                  <input type="radio" :name="'changeUserSelect'" :value="scope.row.userId"
                         v-model="selChangeUserId" style="margin: 0;">
                </template>
              </el-table-column>
              <el-table-column prop="nickName" label="人员姓名" align="center" min-width="120" />
              <el-table-column prop="phone" label="手机号" align="center" min-width="130" />
              <el-table-column prop="email" label="邮箱" align="center" min-width="180" />
              <el-table-column prop="organName" label="人员归属" align="center" min-width="120" />
            </el-table>
          </div>
          <!-- 选中人员信息显示 -->
          <div class="selected-user-info" v-if="selChangeUserInfo">
            <div class="info-item">
              <span class="label">人员姓名：</span>
              <span class="value">{{ selChangeUserInfo.nickName }}</span>
            </div>
            <div class="info-item">
              <span class="label">待处理机会数：</span>
              <span class="value">{{ selChangeUserInfo.taskCount }}</span>
            </div>
            <div class="info-item">
              <span class="label">已参与机会：</span>
              <span class="value">{{ selChangeUserInfo.opportunityCount }}</span>
            </div>
          </div>

          <!-- 重启原因输入 -->
          <div class="restart-reason-section">
            <el-form ref="changeFormRef" :model="changeForm" :rules="changeRules" label-width="0">
              <el-form-item prop="reasonDesc" required>
                <el-input type="textarea" v-model="changeForm.reasonDesc" placeholder="请输入变更原因 (必填)" :rows="4"
                          maxlength="200" show-word-limit>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="form-actions">
            <el-button @click="closeChangePopup" style="width: 120px;">取消</el-button>
            <el-button type="primary" @click="confirmChangeManager" :loading="changeLoading" style="width: 120px;">确认变更</el-button>
          </div>
        </div>
      </div>

    </DtPopup>

  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import {getDicItemList} from "@/config/tool";
import {
  detailPage,
  getOpportunityStatus,
  findLegalOrgDataByTenantId,
  countUserParticipatedOpportunities,
  changeManager
} from "../../api/workbench";
import {getBranchUsers, getLegalList} from "@/api/processManagement";

export default {
  name: "changeManager",
  data() {
    return {
      toolListProps: {
        toolTitle: "项目管理人变更",
        toolList: [
          {
            name: "批量变更",
            btnCode: ""
          }
        ]
      },
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {

        }
      },
      searchFormTemp: [
        {
          label: "总/分公司项目经理",
          name: "name",
          type: "input",
          width: "200px"
        },
        {
          label: "机会名称",
          name: "opportunityName",
          type: "input",
          width: "200px"
        },
        {
          label: "机会ID",
          name: "bizCode",
          type: "input",
          width: "200px"
        },
        {
          label: "机会状态",
          name: "processStep",
          type: "select",
          width: "200px",
          list:[]
        }],
      tableData: [{}],
      total: 0,
      opportunityTypes:[{dicItemCode:"1",dicItemName:"员服"},{dicItemCode:"2",dicItemName:"综合"}],
      closeReasonTypes:[{dicItemCode:1,dicItemName:"机会已成交"},{dicItemCode:2,dicItemName:"机会推进失败"},{dicItemCode:3,dicItemName:"无效机会"}],

      showChangePopup:false,
      selopportunity:null,
      changeSearchForm: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '', // 默认选择分公司项目经理
          organCode: "",
          nickName: ''
        }
      },
      changeSearchFormTemp: [
        { label: '指派对象', name: 'roleType', clearable: false, type: 'select', width: '140px', list: [{ dicItemCode: '3', dicItemName: '分公司项目经理' }, { dicItemCode: '4', dicItemName: '总公司项目经理' }, { dicItemCode: '7', dicItemName: '营业部内勤' }] },
        { label: '机构', name: 'organCode', clearable: false, type: 'select', width: '140px', list: [] },
        { label: '人员姓名', name: 'nickName', type: 'input', width: '140px' }
      ],
      changeUserList:[],
      // 缓存接口数据
      cachedLegalOrgData: {},
      selChangeUserId:null,
      selChangeUserInfo: null, // 新增：用于存储选中人员的信息
      changeForm: {
        reasonDesc: ""
      },
      changeRules: {
        reasonDesc: [
          { required: true, message: "请输入变更说明", trigger: "blur" },
          { min: 1, max: 200, message: "长度在 1 到 200 个字符", trigger: "blur" }
        ]
      },
      changeLoading: false,
      selOpportunitys:[]


    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {
    getOpportunityTypeName(list, opportunityType) {

      let index = list.findIndex((v) => v.dicItemCode == opportunityType);
      return index > -1 ? list[index].dicItemName : "";
      return "";
    },
    getCloseReasonDesc(list, closeReasonType) {
      let index = list.findIndex((v) => v.dicItemCode == closeReasonType);
      return index > -1 ? list[index].dicItemName : "";
    }
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {

    handleSelectionChange(val){
      // console.log(val,"val");
      this.selOpportunitys =val;
    },

    manageHandleTool(item){
      if (item.name == "批量变更") {
        if(!this.selOpportunitys || this.selOpportunitys.length == 0){
          this.$message.error("请选择要变管理人更的机会");
          return;
        }

        this.changeUserList=[];
        this.changeSearchForm = {
          pageNum: 1,
          pageSize: 10,
          param: {
            roleType: '', // 默认选择分公司项目经理
            organCode: "",
            nickName: ''
          }
        };
        this.changeForm.reasonDesc="";
        this.showChangePopup = true;
        this.selopportunity=null;
      }
    },

    changeManager(row){
      // console.log(row);
      this.changeUserList=[];
      this.changeSearchForm = {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleType: '', // 默认选择分公司项目经理
          organCode: "",
          nickName: ''
        }
      };
      this.changeForm.reasonDesc="";
      this.selopportunity = row;
      this.selOpportunitys =null;
      this.showChangePopup = true;

    },

    // 搜索条件联动，item 搜索项，data 搜索参数
    handleChangeSearch(item, data) {
      console.log(item,"item");
      console.log(data,"data");
      // this.restartCloseSearchForm = data;

      // // 如果是指派对象变化，需要更新机构列表
      if (item && item.name === 'roleType') {
        this.handleRoleTypeChange(data.param.roleType, this.changeSearchFormTemp, this.changeSearchForm);
      }
      //
      this.handleChangeSearchFun();
    },

    async handleChangeSearchFun() {
      // console.log(this.changeSearchForm);
      let res = await getBranchUsers(this.changeSearchForm.param);
      if (res && _.isArray(res)) {
        this.changeUserList = res;
      } else {
        this.changeUserList = [];
      }
      // 清空之前选中的用户状态
      this.selChangeUserId = "";
      this.selChangeUserInfo = null;
    },

    // 处理指派对象变化时的机构列表更新
    async handleRoleTypeChange(roleType, searchFormTemp, searchForm) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
        searchForm.param.organCode = "";
      } else {
        // 选择其他选项时，使用缓存数据更新机构列表
        // 根据不同的搜索表单确定对应的业务类型
        let tenantIdParam = null;
        if (searchFormTemp === this.changeSearchFormTemp) {
          tenantIdParam = this.restartCloseBusinessType;
        }

        const tenantId = tenantIdParam || "T0001";

        // 检查缓存中是否已有数据
        if (!this.cachedLegalOrgData[tenantId]) {
          const res = await findLegalOrgDataByTenantId({ tenantId });
          if (res) {
            this.cachedLegalOrgData[tenantId] = res;
          } else {
            return;
          }
        }

        const res = this.cachedLegalOrgData[tenantId];
        this.updateOrgListByRoleType(roleType, searchFormTemp, res);
        // 更新机构选择为第一个可用选项
        if (searchFormTemp[1].list && searchFormTemp[1].list.length > 0) {
          searchForm.param.organCode = searchFormTemp[1].list[0].dicItemCode;
        }
      }
      // 清空之前选中的用户状态
      // this.selectedRestartCloseUserId = "";
      // this.selectedRestartCloseUserInfo = null;

    },

    // 根据指派对象类型更新机构列表
    updateOrgListByRoleType(roleType, searchFormTemp, res) {
      // 判断是否为总公司相关选项
      const isHeadquartersRole = roleType === '2' || roleType === '4'; // 2:总公司统筹, 4:总公司项目经理

      if (isHeadquartersRole) {
        // 选择总公司相关选项时，机构列表固定为总公司
        searchFormTemp[1].list = [{ dicItemCode: "", dicItemName: "总公司" }];
      } else {
        // 选择其他选项时，使用接口返回的orgList
        const orgList = res.orgList ? res.orgList.map(item => ({ dicItemCode: item.orgCode, dicItemName: item.orgName })) : [];
        searchFormTemp[1].list = orgList;
      }
    },

    // 点击人员时获取人员的任务数
    async handleChangeUserSelect(row) {
      if (!row) {
        this.selChangeUserInfo = null;
        return;
      }
      this.selChangeUserId = row.userId;
      try {
        // 调用接口获取参与机会数和待完成任务数
        const res = await countUserParticipatedOpportunities({
          userId: row.userId
        });

        if (res) {
          this.selChangeUserInfo = {
            ...row,
            opportunityCount: res.opportunityCount || 0,
            taskCount: res.taskCount || 0
          };
        } else {
          // 如果接口调用失败，使用默认值
          this.selChangeUserInfo = {
            ...row,
            opportunityCount: 0,
            taskCount: 0
          };
        }
      } catch (error) {
        // 接口调用失败时使用默认值
        this.selChangeUserInfo = {
          ...row,
          opportunityCount: 0,
          taskCount: 0
        };
      }
    },

    async confirmChangeManager(){
      if (!this.selChangeUserId) {
        this.$message.warning("请选择指派人员");
        return;
      }

      try {
        await this.$refs.changeFormRef.validate();
        this.changeLoading = true;
        let param = {changeRemark:this.changeForm.reasonDesc,data:[]};
        if(this.selopportunity !=null){
          param.data.push({opportunityId:this.selopportunity.id,userId:this.selChangeUserId,roleType:this.changeSearchForm.param.roleType});

        } else if(this.selOpportunitys !=null && this.selOpportunitys.length > 0){
          console.log(this.selOpportunitys);
          this.selOpportunitys.forEach(item => {
            param.data.push({opportunityId:item.id,userId:this.selChangeUserId,roleType:this.changeSearchForm.param.roleType});

          });
        } else {
          this.$message.warning("未找到指派人员");
          return;
        }


        let res = await changeManager(param);
        if (res) {
          this.$message.success(res.resp_msg);
          this.getDetailPage();
          this.showChangePopup = false;
        }

      } catch (error) {
        this.$message.error(error);
      } finally {
        this.changeLoading = false;
      }
    },

    // 检查用户是否有指定权限
    hasPermission(permissionCode) {

      if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuthStr) {
        return false;
      }
      const roleAuthStr = this.qikeUserInfo.roleAuthStr;
      const permissions = roleAuthStr.split(',').map(item => item.trim());
      return permissions.includes(permissionCode);
      // return true;

    },


    initData() {
      this.getDicFun();
      this.getDetailPage();
    },
    async getDetailPage(){

      let param = _.cloneDeep(this.initParam);

      if(param.param.processStep){
        let ss = param.param.processStep;
        param.param.opportunityStatus = parseInt(ss.split("-")[0]);
        param.param.processStep = ss.split("-")[1];
      }

      const res = await detailPage(param);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.searchFormTemp = this.$options.data().searchFormTemp;
      this.initData();
      this.getDicFun();
    },
    async getDicFun() {

      await getDicItemList("elms.opportunity.status");
      await getDicItemList("gen.yesorno.num");

      const statusList = await getOpportunityStatus({});
      if(statusList){
        // console.log(statusList,"statusList");
        // 状态
        this.searchFormTemp[3].list = statusList;
      }


    },

    detail(row) {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: row.id,
          title:"项目管理人变更"
        }
      });
    },

    closeChangePopup(){
      this.showChangePopup = false;
      this.selChangeUserId = "";
      this.selChangeUserInfo = null;
    },





  }
};
</script>

<style lang="less" scoped>

.popup-content {
  padding: 0 20px 20px 20px;
  max-height: 600px;
  overflow-y: auto;

  // 在小屏幕上调整最大高度
  @media (max-height: 800px) {
    max-height: 500px;
  }

  @media (max-height: 600px) {
    max-height: 400px;
  }

  .business-type-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;

    .section-title {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 12px;
      color: #333;
    }

    .el-radio-group {
      .el-radio {
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .table-container {
    margin-top: 10px;
    margin-bottom: 10px;
    max-height: 300px;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    // Firefox
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;

    // IE
    -ms-overflow-style: auto;

    // 为表格设置固定高度和滚动
    .el-table {
      .el-table__body-wrapper {
        overflow-y: auto;
      }

      // 确保表格在小屏幕上也能正常显示
      .el-table__header-wrapper {
        background-color: #f5f7fa;
      }

      .el-table__body {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  .restart-close-tip {
    margin-bottom: 20px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    text-align: center;
  }
}

.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}
.member-manage {
  .text-content{
    width: 100%;
    text-align: left;
  }
  .text-content div{
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px; /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto; /* 添加滚动条 */
      padding-right: 10px; /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

// 选中人员信息显示样式
.selected-user-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f2f5;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .info-item {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;

    .label {
      font-weight: bold;
      margin-right: 8px;
      min-width: 80px;
    }

    .value {
      color: v-bind('themeObj.color');
      flex: 1;
    }
  }

  // 在小屏幕上调整布局
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 10px;

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      min-width: auto;

      .label {
        margin-bottom: 4px;
        min-width: auto;
      }
    }
  }
}

// 重启原因输入样式
.restart-reason-section {
  width: 100%;
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .el-form-item {
    margin-bottom: 0;
  }

  .el-textarea {
    .el-textarea__inner {
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
      }
    }
  }
}

</style>
