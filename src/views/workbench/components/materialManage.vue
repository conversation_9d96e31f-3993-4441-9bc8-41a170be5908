<template>
  <div class="member-manage">
    <!-- 生态服务 -->
    <div v-if="authPerm.ecologyView">
      <TableToolTemp :toolListProps="serviceMenuListPros" class="log-tool"></TableToolTemp>
      <el-form ref="detailForm" :model="detailForm" label-width="150px" label-position="left">
        <el-form-item label="是否附加生态服务" prop="attchService">
          <el-radio-group v-model="detailForm.attchService" @change="detailDataChange" :disabled="authPerm.editDisable || !authPerm.ecologyOperate">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-table :data="serviceProductList" class="dt-table" style="width: 100%" v-hover row-key="id" v-if="detailForm.attchService==1">
        <el-table-column align="center" prop="companyCode" label="服务类型" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.serviceType }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="policyNo" label="服务产品" width="250">
          <template slot-scope="scope">
            <div>
              <a  :style="{ color: themeObj.color }"
                 @click="opendProductInfoPopup(scope.row)">{{ scope.row.productName }}</a>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="scope">
            <div class="action-buttons" v-if="!authPerm.editDisable && authPerm.ecologyOperate">

              <el-button class="btn-center" type="text" @click="opendProductPopup()">
                选择产品
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <!-- 投标情况 -->
    <div v-if="authPerm.biddingView">
      <TableToolTemp :toolListProps="bideMenuListPros" class="log-tool"></TableToolTemp>
      <el-form ref="detailForm" :model="detailForm" label-width="150px" label-position="left">
        <el-form-item label="是否投标" prop="divisionId">
          <el-radio-group v-model="detailForm.isBid"  @change="detailDataChange" :disabled="authPerm.editDisable  || !authPerm.biddingOperate">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="投标时间" prop="divisionId" v-if="detailForm.isBid==1">
          <el-date-picker v-model="detailForm.bidDateList" type="datetimerange"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="yyyy-MM-dd HH:mm:ss " value-format="yyyy-MM-dd HH:mm:ss"  @blur="detailDataChange" :disabled="authPerm.editDisable || !authPerm.biddingOperate">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="投标结果" prop="divisionId" v-if="detailForm.isBid==1">
          <el-radio-group v-model="detailForm.bidResult"  @change="detailDataChange" :disabled="authPerm.editDisable || !authPerm.biddingOperate">
            <el-radio :label="1">成功</el-radio>
            <el-radio :label="0">失败</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="detail-upload-btn" v-if="detailForm.isBid==1 && !authPerm.editDisable && authPerm.biddingOperate">
        <el-upload
          icon="el-icon-upload2"
          class="upload-demo downd_excedl"
          :multiple="false"
          style="display: inline-block; margin: 0 10px"
          :auto-upload="true"
          list-type="text"
          :data="detailFileParam"
          :show-file-list="false"
          :action="detailFileUploadUrl"
          :before-upload="bidBeforeUpload"
          :on-success="uploadSuccess"
          :headers="{
              access_token: currentLoginUser.access_token,
              tenantId: currentLoginUser.tenantId,
              funcId: currentLoginUser.funcId,
            }"
        >
          <el-button type="primary">上传文件</el-button>
        </el-upload>
      </div>

      <el-table :data="bidFileList" class="dt-table" style="width: 100%" v-hover row-key="id" v-if="detailForm.isBid==1">
        <el-table-column align="center" prop="fileName" label="标书文件" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createName" label="上传人" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="上传时间" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="scope">
            <div class="action-buttons"  >
              <el-button class="btn-center" type="text" @click="removeDetailFile(scope.row.id)" v-if="!authPerm.editDisable && authPerm.biddingOperate">
                删除
              </el-button>
              <el-button class="btn-center" type="text" @click="detailFileDownload(scope.row)" v-if="authPerm.biddingDownload">
                下载
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>


    <!-- 经纪委托授权书 -->
    <div v-if="authPerm.authorizationView && detailForm.isBid==1 && detailForm.bidResult==1">
      <TableToolTemp :toolListProps="authMenuListPros" class="log-tool" ></TableToolTemp>
      <div class="detail-upload-btn" v-if="!authPerm.editDisable && authPerm.authorizationOperate">
        <el-upload
          icon="el-icon-upload2"
          class="upload-demo downd_excedl"
          :multiple="false"
          style="display: inline-block; margin: 0 10px"
          :auto-upload="true"
          list-type="text"
          :data="detailFileParam"
          :show-file-list="false"
          :action="detailFileUploadUrl"
          :before-upload="authBeforeUpload"
          :on-success="uploadSuccess"
          :headers="{
              access_token: currentLoginUser.access_token,
              tenantId: currentLoginUser.tenantId,
              funcId: currentLoginUser.funcId,
            }"
        >
          <el-button type="primary">上传文件</el-button>
        </el-upload>
      </div>

      <el-table :data="authFileList" class="dt-table" style="width: 100%" v-hover row-key="id" >
        <el-table-column align="center" prop="fileName" label="委托授权书文件" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createName" label="上传人" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="上传时间" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="scope">
            <div class="action-buttons"  v-if="!authPerm.editDisable">
              <el-button class="btn-center" type="text" @click="removeDetailFile(scope.row.id)"  v-if="!authPerm.editDisable && authPerm.authorizationOperate">
                删除
              </el-button>
              <el-button class="btn-center" type="text" @click="detailFileDownload(scope.row)" v-if="authPerm.authorizationDownload">
                下载
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 询价记录 -->
    <div v-if="authPerm.inquiryView && ((detailForm.isBid==1 && detailForm.bidResult==1) ||  detailForm.isBid==0)">
      <TableToolTemp :toolListProps="inquiryMenuListPros" class="log-tool" ></TableToolTemp>
      <div class="detail-upload-btn" v-if="!authPerm.editDisable && authPerm.inquiryOperate">
        <el-upload
          icon="el-icon-upload2"
          class="upload-demo downd_excedl"
          :multiple="false"
          style="display: inline-block; margin: 0 10px"
          :auto-upload="true"
          list-type="text"
          :data="detailFileParam"
          :show-file-list="false"
          :action="detailFileUploadUrl"
          :before-upload="inquiryBeforeUpload"
          :on-success="uploadSuccess"
          :headers="{
              access_token: currentLoginUser.access_token,
              tenantId: currentLoginUser.tenantId,
              funcId: currentLoginUser.funcId,
            }"
        >
          <el-button type="primary">上传文件</el-button>
        </el-upload>
      </div>

      <el-table :data="inquiryFileList" class="dt-table" style="width: 100%" v-hover row-key="id" >
        <el-table-column align="center" prop="fileName" label="询价记录文件" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createName" label="上传人" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="上传时间" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="scope">
            <div class="action-buttons" >
              <el-button class="btn-center" type="text" @click="removeDetailFile(scope.row.id)" v-if="!authPerm.editDisable && authPerm.inquiryOperate">
                删除
              </el-button>
              <el-button class="btn-center" type="text" @click="detailFileDownload(scope.row)" v-if="authPerm.inquiryDownload">
                下载
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 排分结果 -->
    <div v-if="authPerm.resultView && ((detailForm.isBid==1 && detailForm.bidResult==1) ||  detailForm.isBid==0)">
      <TableToolTemp :toolListProps="resultMenuListPros" class="log-tool"></TableToolTemp>
      <div class="detail-upload-btn" v-if="!authPerm.editDisable && authPerm.resultOperate">
          <el-button type="primary" @click="openFileUploadPop">上传文件</el-button>
      </div>

      <el-table :data="resultFileList" class="dt-table" style="width: 100%" v-hover row-key="id" >
        <el-table-column align="center" prop="fileName" label="保险公司" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.companyName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="fileName" label="排分结果文件" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createName" label="上传人" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="上传时间" width="250">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template slot-scope="scope">
            <div class="action-buttons" >
              <el-button class="btn-center" type="text" @click="removeDetailFile(scope.row.id)" v-if="!authPerm.editDisable && authPerm.resultOperate">
                删除
              </el-button>
              <el-button class="btn-center" type="text" @click="detailFileDownload(scope.row)" v-if="authPerm.resultDownload">
                下载
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 选择产品弹窗 -->
    <DtPopup :isShow.sync="showProductPopup" @close="showProductPopup = false" @confirm="chooseProduct" title="生态服务产品" center :footer="productMode=='edit'?true:false"
      width="1200px">
      <div class="productContainer" >
        <!-- 选择产品 -->
        <div class="product-left" v-if="productMode == 'edit'">
          <el-table :data="healthProductList" class="dt-table" style="width: 100%" v-hover row-key="id"
            @current-change="getProductInfo">
            <el-table-column width="55" fixed="left">
              <template slot-scope="scope">
                <el-radio class="radio" :label="scope.row" v-model="selHealthProduct">{{ "" }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="productName" label="健康服务" width="250">
              <template slot-scope="scope">
                <span class="communication-item">{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-table :data="rescueProductList" class="dt-table" style="width: 100%" v-hover row-key="id"
            @current-change="getProductInfo">
            <el-table-column width="55" fixed="left">
              <template slot-scope="scope">
                <el-radio class="radio" :label="scope.row" v-model="selRescueProduct">{{ "" }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="productName" label="救援服务" width="250">
              <template slot-scope="scope">
                <span class="communication-item">{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 产品详情渲染 -->
        <div class="product-right" v-if="productInfo.productName !=''" :style="productMode=='edit'?'width:70%':'width:100%'">
          <div class="productInfo">
            <div class="title"><span>{{ productInfo.productName }}</span></div>
            <div class="sub_title"><span>购买条件</span></div>
            <div class="sub_title">
              <span>有效期：{{ productInfo.servicePeriod }}{{ productInfo.servicePeriodUnit }}</span>
              <span>年龄范围：{{ productInfo.minAge }}-{{ productInfo.maxAge }}周岁</span>
              <span>生效日期：T+{{ productInfo.startDay }}-T+{{ productInfo.endDay }}</span>
            </div>
            <div class="content">
              <table>
                <thead>
                  <tr>
                    <th v-for="(item, index) in processedTableData.headers" :key="index"
                        :style="{ width: processedTableData.columnWidths[index] }">{{ item }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(subArray, subIndex) in processedTableData.dataList" :key="subIndex">
                    <td v-for="(item, index) in subArray" :key="index">
                      <!-- 内容列的特殊处理 -->
                      <template v-if="index === 1 && Array.isArray(item)">
                        <div class="content-items">
                          <span v-for="(contentItem, contentIndex) in item" :key="contentIndex" class="content-item">
                            <a v-if="contentItem && contentItem.includes('#_#')" :style="{ color: themeObj.color }"
                              @click="getServiceInfo(contentItem)">{{ contentItem.split("#_#")[0] }}</a>
                            <span v-else>{{ contentItem || '' }}</span>
                          </span>
                        </div>
                      </template>
                      <!-- 其他列的正常处理 -->
                      <template v-else>
                        <a v-if="item && item.includes('#_#')" :style="{ color: themeObj.color }"
                          @click="getServiceInfo(item)">{{ item.split("#_#")[0] }}</a>
                        <span v-else>{{ item || '' }}</span>
                      </template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </DtPopup>

    <!-- 服务详情弹窗 -->
    <DtPopup :isShow.sync="showServiceItemPopup" @close="showServiceItemPopup = false" :title="serviceItemInfo.itemTitle"
      center :footer="false">
      <div class="serviceContainer">
        <div v-if="serviceItemInfo.serviceContent" class="serviceItem">
          <div v-html="serviceItemInfo.serviceContent"></div>
        </div>
        <div v-if="serviceItemInfo.validity" class="serviceItem">
          <div class="title">服务时效：</div>
          <span>{{ serviceItemInfo.validity }}</span>
        </div>
        <div v-if="serviceItemInfo.waitingPeriod" class="serviceItem">
          <div class="title">等待期：</div>
          <span>{{ serviceItemInfo.waitingPeriod }}</span>
        </div>
        <div v-if="serviceItemInfo.serviceTarget" class="serviceItem">
          <div class="title">服务对象：</div>
          <span>{{ serviceItemInfo.serviceTarget }}</span>
        </div>
        <div v-if="serviceItemInfo.serviceTimes" class="serviceItem">
          <div class="title">服务次数：</div>
          <span>{{ serviceItemInfo.serviceTimes }}</span>
        </div>
      </div>
    </DtPopup>

    <!-- 排分结果文件上传弹窗 -->
    <DtPopup :isShow.sync="showFileUploadPopup" @close="showFileUploadPopup = false" title="上传文件"
             center :footer="false">
      <div class="serviceContainer">
        <el-select ref="companySel" v-model="fileCompanyCode" filterable  placeholder="请选择" style="width: 400px" >
          <el-option v-for="(item, index) in insureCompanyList" :label="item.comFullName" :value="item.comCode"
                     :key="index">
          </el-option>
        </el-select>

        <el-upload
          icon="el-icon-upload2"
          class="upload-demo downd_excedl"
          :multiple="false"
          style="display: inline-block; margin: 0 10px"
          :auto-upload="true"
          list-type="text"
          :data="detailFileParam"
          :show-file-list="false"
          :action="detailFileUploadUrl"
          :before-upload="resultBeforeUpload"
          :on-success="uploadSuccess"
          :headers="{
            access_token: currentLoginUser.access_token,
            tenantId: currentLoginUser.tenantId,
            funcId: currentLoginUser.funcId,
          }"
        >
          <el-button type="primary">上传文件</el-button>
        </el-upload>
      </div>
    </DtPopup>
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import { rootPath} from "@/utils/globalParam";

import {
  checkManager,
  detailSave,
  esptProductInfo,
  esptProductItemInfo,
  esptProductList, fileList, fileRemove, getCompnayList,
  getDetail,
  updateOrder
} from "../../../api/workbench";
import { getDicItemList } from "../../../config/tool";



export default {
  name: "materialManage",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      opportunityId: this.$route.query.id,
      authPerm:{
        editDisable:true, // 项目状态导致禁止编辑
        ecologyView:false, // 生态服务查看
        ecologyOperate:false,// 生态服务操作
        biddingView:false,// 竞标情况查看
        biddingDownload:false, // 竞标下载
        biddingOperate:false, // 竞标操作
        authorizationView:false,// 经纪委托书查看
        authorizationDownload:false, // 经纪委托书下载
        authorizationOperate:false, // 经纪委托书操作
        inquiryView:false,// 询价查看
        inquiryDownload:false, // 询价下载
        inquiryOperate:false, // 询价操作
        resultView:false,// 排分查看
        resultDownload:false, // 排分下载
        resultOperate:false, // 排分操作
      },


      detailForm: {
        id:null,
        opportunityId:null,
        attchService:null,
        isBid:null,
        bidResult:null,
        bidStartDate:null,
        bidEndDate:null,
        // addHealthService:"",
        healthServiceCode:"",
        healthServiceName:"",
        // addRescueService:"",
        rescueServiceCode:"",
        rescueServiceName:"",
        bidDateList: [],
      },

      /*** 资料文件 ***/
      detailFileParam:{
        opportunityId:this.opportunityId,
        fileType:"bid",
        companyId:"",
        companyName:""
      },
      detailFileUploadUrl:`${rootPath}/api/opportunity/file/upload`,
      bidFileList:[],
      authFileList:[],
      inquiryFileList:[],
      resultFileList:[],
      insureCompanyList:[],


      /*** 生态服务 ***/
      productMode:"edit",
      serviceMenuListPros: {
        toolTitle: "生态服务"
      },
      // 服务产品列表
      serviceProductList: [
        { serviceType: "健康服务", productName: "" },
        { serviceType: "救援服务", productName: "" }
      ],
      fileCompanyCode:"",
      showProductPopup: false,
      showServiceItemPopup: false,
      showFileUploadPopup: false,
      // 健康服务产品列表
      healthProductList: [],
      // 救援服务产品列表
      rescueProductList: [],
      selHealthProduct: {},
      selRescueProduct: {},
      // 产品详情
      productInfo: {
        productCode: "",
        productName: "",
        servicePeriod: null,
        servicePeriodUnit: "",
        minAge: null,
        maxAge: null,
        startDay: null,
        endDay: null,
        headers: [],
        dataList: []
      },
      // 服务详情
      serviceItemInfo: {
        itemTitle: "",
        serviceContent: "",
        serviceCode: "",
        validity: "",
        serviceTarget: "",
        serviceTimes: "",
        waitingPeriod: ""
      },
      // 救援类型数组
      rescueServiceItemType: [],

      /*** 投标情况 ***/
      bideMenuListPros: {
        toolTitle: "投标情况"
      },

      /*** 经纪委托授权书 ***/
      authMenuListPros: {
        toolTitle: "经纪委托授权书"
      },

      /*** 询价记录 ***/
      inquiryMenuListPros: {
        toolTitle: "询价记录"
      },

      /*** 排分结果 ***/
      resultMenuListPros: {
        toolTitle: "排分结果"
      },

    }
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {

  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 获取企客用户权限信息
    qikeUserInfo() {
      return this.$store.getters["layoutStore/getQikeUserInfo"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    },
         // 处理表格数据，确保头部铺满，数据行以最长的行为模板填充
     processedTableData() {
       if (!this.productInfo.headers || !this.productInfo.dataList) {
         return {
           headers: [],
           dataList: [],
           columnWidths: []
         };
       }

       const headers = this.productInfo.headers;
       const dataList = this.productInfo.dataList;

       // 检查是否有"内容"列
       const contentColumnIndex = headers.findIndex(header => header === '内容');

       if (contentColumnIndex !== -1) {
         // 有内容列的情况：重新组织数据结构
         const processedHeaders = [];
         const processedDataList = [];

         // 第一列：服务项目/计划
         processedHeaders.push(headers[0]);

         // 第二列：内容（合并所有后续列）
         processedHeaders.push('内容');

         // 处理数据行
         dataList.forEach(row => {
           const processedRow = [];

           // 第一列：服务项目/计划
           processedRow.push(row[0] || '');

           // 第二列：内容（合并所有后续数据）
           const contentItems = row.slice(1).filter(item => item && item.trim() !== '');
           processedRow.push(contentItems);

           processedDataList.push(processedRow);
         });

         // 设置列宽：第一列固定，第二列铺满剩余空间
         const columnWidths = ['140px', '1fr'];

         return {
           headers: processedHeaders,
           dataList: processedDataList,
           columnWidths: columnWidths
         };
       } else {
         // 没有内容列的情况：多列表格，需要合理分配列宽
         const maxLength = Math.max(...dataList.map(row => row.length), headers.length);

         const processedHeaders = [...headers];
         while (processedHeaders.length < maxLength) {
           processedHeaders.push('');
         }

         const processedDataList = dataList.map(row => {
           const processedRow = [...row];
           while (processedRow.length < maxLength) {
             processedRow.push('');
           }
           return processedRow;
         });

         // 为多列表格设置合理的列宽
         const columnWidths = processedHeaders.map((header, index) => {
           if (index === 0) {
             // 第一列（服务项目/计划）设置较宽
             return '160px';
           } else {
             // 其他列设置较窄，确保所有列都能显示
             return '70px';
           }
         });

         return {
           headers: processedHeaders,
           dataList: processedDataList,
           columnWidths: columnWidths
         };
       }
     }
  },
  async created() {
     // console.log(this.injectedOpportunityDetail);
    this.initData();


  },
  methods: {

    // 检查用户是否有指定权限
    hasPermission(permissionCode) {

      if (!this.qikeUserInfo || !this.qikeUserInfo.roleAuthStr) {
        return false;
      }
      const roleAuthStr = this.qikeUserInfo.roleAuthStr;
      const permissions = roleAuthStr.split(',').map(item => item.trim());
      return permissions.includes(permissionCode);
      // return true;

    },
    checkPerms(){
      if(this.$route.query.title=="机会管理"){
        // 从机会管理跳过来，一定不能编辑
        this.authPerm.editDisable = true;
      } else if(this.injectedOpportunityDetail  && this.injectedOpportunityDetail.opportunity
        && this.injectedOpportunityDetail.opportunity.status && this.injectedOpportunityDetail.opportunity.status==1){
        this.authPerm.editDisable = false;
      } else {
        this.authPerm.editDisable = true;
      }

      this.authPerm.ecologyView =  this.hasPermission("elms:opportunity:data:ecology:view");
      this.authPerm.ecologyOperate =  this.hasPermission("elms:opportunity:data:ecology:operate");
      this.authPerm.biddingView  =  this.hasPermission("elms:opportunity:data:bidding:view");
      this.authPerm.biddingDownload  =  this.hasPermission("elms:opportunity:data:bidding:download");
      this.authPerm.biddingOperate  =  this.hasPermission("elms:opportunity:data:bidding:operate");
      this.authPerm.authorizationView  =  this.hasPermission("elms:opportunity:data:authorization:view");
      this.authPerm.authorizationDownload  =  this.hasPermission("elms:opportunity:data:authorization:download");
      this.authPerm.authorizationOperate  = this.hasPermission("elms:opportunity:data:authorization:operate");
      this.authPerm.inquiryView  = this.hasPermission("elms:opportunity:data:inquiry:view");
      this.authPerm.inquiryDownload  =  this.hasPermission("elms:opportunity:data:inquiry:download");
      this.authPerm.inquiryOperate  = this.hasPermission("elms:opportunity:data:inquiry:operate");
      this.authPerm.resultView  =  this.hasPermission("elms:opportunity:data:result:view");
      this.authPerm.resultDownload  = this.hasPermission("elms:opportunity:data:result:download");
      this.authPerm.resultOperate  = this.hasPermission("elms:opportunity:data:result:operate");

      console.log(this.authPerm, "authPerm" );
    },


    initData() {
      this.checkPerms();
      this.getEsptProductList();
      this.getRescueServiceItemType();
      this.getDetail();
      this.getDetailFiles();
      this.getInsureCompanyList();
    },

    openFileUploadPop(){
      this.showFileUploadPopup = true;
    },

    async getInsureCompanyList() {
      const res = await getCompnayList();
      if(res && !res.resp_msg){
        this.insureCompanyList = res;
      }
    },

    // 导入之前检查文件类型
    bidBeforeUpload(file) {
      this.detailFileParam.opportunityId = this.opportunityId;
      this.detailFileParam.fileType="bid";
      return true;
    },
    authBeforeUpload(file) {
      this.detailFileParam.opportunityId = this.opportunityId;
      this.detailFileParam.fileType="auth";

      return true;
    },
    resultBeforeUpload(file) {
      if(!this.fileCompanyCode){
        this.$message.error("请选择保险公司");
        return false;
      }
      this.detailFileParam.companyId = this.fileCompanyCode;
      this.insureCompanyList.forEach(row => {
        if(row.comCode == this.fileCompanyCode){
          this.detailFileParam.companyName = row.comFullName;
        }
      });

      this.detailFileParam.opportunityId = this.opportunityId;
      this.detailFileParam.fileType="result";
      return true;
    },
    inquiryBeforeUpload(file) {
      this.detailFileParam.opportunityId = this.opportunityId;
      this.detailFileParam.fileType="inquiry";
      return true;
    },
    // 上传成功
    uploadSuccess(response, file, fileList) {
      console.log(response);
      if (response.resp_code == 0) {
        this.showFileUploadPopup = false;
        this.getDetailFiles();
      } else {
        this.$message.error(response.resp_msg);
      }
    },

    //  保存机会明细
    async getDetail() {
      let params = {opportunityId: this.opportunityId };
      const response = await getDetail(params);
      if (response && !response.resp_msg) {
        this.detailForm = response;

        // 处理投标日期
        if(this.detailForm.bidStartDate && this.detailForm.bidEndDate){
          this.detailForm.bidDateList=[this.detailForm.bidStartDate,this.detailForm.bidEndDate];
        }

        // 处理救援产品数据
        this.serviceProductList =[];

        if(this.detailForm.healthServiceCode){
          let productCode = this.detailForm.healthServiceCode.split(",");

          this.serviceProductList.push({serviceType: "健康服务",
            productName: this.detailForm.healthServiceName,
            productId:productCode[0],
            productCode:productCode[1]
          });
        } else {
          this.serviceProductList.push({serviceType: "健康服务", productName: ""});
        }

        if(this.detailForm.rescueServiceCode){
          let productCode = this.detailForm.rescueServiceCode.split(",");

          this.serviceProductList.push({serviceType: "救援服务",
            productName: this.detailForm.rescueServiceName,
            productId:productCode[0],
            productCode:productCode[1]
          });
        } else {
          this.serviceProductList.push({serviceType: "救援服务", productName: ""});
        }


      }
    },

    detailDataChange(){
      console.log(this.detailForm);
      if(this.detailForm.bidDateList && this.detailForm.bidDateList.length > 0){
        this.detailForm.bidStartDate=this.detailForm.bidDateList[0];
        this.detailForm.bidEndDate=this.detailForm.bidDateList[1];
      }
      this.detailSave();
    },

    //  保存机会明细
    async detailSave() {
      if(!this.detailForm.opportunityId){
        this.detailForm.opportunityId=this.opportunityId;
      }
      let _param = _.cloneDeep(this.detailForm);
      const response = await detailSave(_param);
      if (response) {
        console.log(response);
      }
    },

    /*** 资料文件 ***/

    // 下载
    detailFileDownload(item) {
      window.open(item.filePath);
    },
    async removeDetailFile(id){
      const response = await fileRemove({id:id});
      if (response) {
        this.$message.success(response.resp_msg);
        this.getDetailFiles();
      }
    },

    async getDetailFiles(){
      const response = await fileList({opportunityId:this.opportunityId});
      if (response && !response.resp_msg) {
        this.bidFileList=[];
        this.authFileList=[];
        this.inquiryFileList=[];
        this.resultFileList=[];
        response.forEach(item => {
          if(item.fileType == 'bid'){
            this.bidFileList.push(item);
          }
          if(item.fileType == 'auth'){
            this.authFileList.push(item);
          }
          if(item.fileType == 'inquiry'){
            this.inquiryFileList.push(item);
          }
          if(item.fileType == 'result'){
            this.resultFileList.push(item);
          }
        });
      }
    },


    /*** 生态服务 ***/
    chooseProduct() {
      // console.log(this.selHealthProduct);
      // console.log(this.selRescueProduct);

      this.serviceProductList =[];
      if(this.selHealthProduct){
        // this.detailForm.addHealthService="1";
        this.detailForm.healthServiceCode =this.selHealthProduct.productId+","+this.selHealthProduct.productCode;
        this.detailForm.healthServiceName =this.selHealthProduct.productName;
        this.serviceProductList.push({serviceType: "健康服务",
          productName: this.selHealthProduct.productName,
          productId:this.selHealthProduct.productId,
          productCode:this.selHealthProduct.productCode
        });

      }
      if(this.selRescueProduct){
        // this.detailForm.addRescueService ="1";
        this.detailForm.rescueServiceCode =this.selRescueProduct.productId+","+this.selRescueProduct.productCode;
        this.detailForm.rescueServiceName =this.selRescueProduct.productName;
        this.serviceProductList.push({serviceType: "救援服务",
          productName: this.selRescueProduct.productName,
          productId:this.selRescueProduct.productId,
          productCode:this.selRescueProduct.productCode
        });

      }
      this.detailSave();

      this.showProductPopup=false;
    },
    async getRescueServiceItemType() {
      const response = await getDicItemList("espt.rescueServiceItemType");
      if (response && !response.resp_msg) {
        this.rescueServiceItemType = response;
      }
    },
    getServiceInfo(item) {
      // console.log(item);
      if (item.includes("#_#")) {
        let itemName = item.split("#_#")[0];
        let itemId = item.split("#_#")[1];
        if (itemId) {
          this.getEsptItemInfo(itemId, itemName);
        }
      }
    },
    async getEsptItemInfo(itemId, itemName) {
      let param = { serviceItemId: itemId };
      const res = await esptProductItemInfo(param);
      if (res && !res.resp_msg) {
        this.serviceItemInfo = res;
        this.serviceItemInfo.itemTitle = itemName
        this.showServiceItemPopup = true;
      }
    },
    getProductInfo(row, column, event) {
      this.getEsptProductInfo(row);
    },

    async getEsptProductInfo(row) {
      const res = await esptProductInfo(row);
      if (res && !res.resp_msg) {
        // console.log(res);
        this.productInfo.productCode = res.productCode;
        this.productInfo.productName = res.productName;
        this.productInfo.servicePeriod = res.servicePeriod;
        this.productInfo.servicePeriodUnit = res.servicePeriodUnit;
        this.productInfo.minAge = res.minAge;
        this.productInfo.maxAge = res.maxAge;
        this.productInfo.startDay = res.startDay;
        this.productInfo.endDay = res.endDay;
        this.productInfo.headers = [];
        this.productInfo.dataList = [];
        // 对不同类型分开处理，每类的数据不一样
        if (res.planInfoList) {
          // 该类型第一行为头部
          this.productInfo.headers = res.planInfoList[0];
          for (var i = 1; i < res.planInfoList.length; i += 1) {
            this.productInfo.dataList.push(res.planInfoList[i]);
          }
        } else if (res.serviceItemInfoList) {
          this.productInfo.headers = ["服务项目/计划"];
          for (var i = 0; i < res.serviceItemInfoList.length; i += 1) {
            let item = [res.serviceItemInfoList[i]];
            this.productInfo.dataList.push(item);
          }
        } else if (res.rescueServiceItemInfoList) {
          this.productInfo.headers = ["服务项目/计划", "内容"];
          // 救援项目
          for (var i = 0; i < res.rescueServiceItemInfoList.length; i += 1) {
            let itemArray = res.rescueServiceItemInfoList[i].itemList;

            // 找到类型对应的字典作为头部
            let typeCode = res.rescueServiceItemInfoList[i].typeCode;
            if (typeCode) {
              typeCode = typeCode.toString();
              this.rescueServiceItemType.forEach(item => {
                // console.log(item.dicItemId +"  "+typeCode+(item.dicItemId == typeCode));
                if (item.dicItemCode == typeCode) {
                  itemArray.unshift(item.dicItemName);
                }
              });
            }
            this.productInfo.dataList.push(itemArray);
          }

          console.log(this.productInfo);
        }


      }
    },
    opendProductPopup() {
      this.productMode='edit';
      this.productInfo = {
        productCode: "",
        productName: "",
        servicePeriod: null,
        servicePeriodUnit: "",
        minAge: null,
        maxAge: null,
        startDay: null,
        endDay: null,
        headers: [],
        dataList: []
      };
      this.showProductPopup = true;
    },

    opendProductInfoPopup(row) {
      this.productMode='info';
      this.productInfo = {
        productCode: "",
        productName: "",
        servicePeriod: null,
        servicePeriodUnit: "",
        minAge: null,
        maxAge: null,
        startDay: null,
        endDay: null,
        headers: [],
        dataList: []
      };
      this.getEsptProductInfo(row);
      this.showProductPopup = true;
    },
    async getEsptProductList() {
      const res = await esptProductList();
      if (res && !res.resp_msg) {
        if (res.healthProductList) {
          this.healthProductList = res.healthProductList;
        }
        if (res.rescueProductList) {
          this.rescueProductList = res.rescueProductList;
        }
      }

    },


  }
};
</script>

<style lang="less" scoped>
.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}

.member-manage {
  .text-content {
    width: 100%;
    text-align: left;
  }

  .text-content div {
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px;
    /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto;
      /* 添加滚动条 */
      padding-right: 10px;
      /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0;
      /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

.productContainer {
  display: flex;
  /* 启用Flexbox */
  width: 100%;
  height: 500px;
  /* 设置弹窗固定高度 */
  overflow: hidden;
  /* 防止整体出现滚动条 */
}

.product-left {
  width: 30%;
  /* 或者具体的像素值 */
  height: 100%;
  overflow-y: auto;
  /* 添加垂直滚动 */
  padding-right: 10px;
  /* 为滚动条留出空间 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 表格样式调整 */
  .el-table {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.product-right {
  width: 70%;
  /* 或者具体的像素值 */
  height: 100%;
  overflow-y: auto;
  /* 添加垂直滚动 */
  padding-left: 20px;
  /* 为左侧留出间距 */
  padding-right: 10px;
  /* 为滚动条留出空间 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.product-right .productInfo {
  width: 100%;

  .title {
    color: black;
    font-weight: bolder;
    font-size: 16px;
    width: 100%;
    text-align: center;
    margin: 10px 10px 10px 10px;
  }

  .sub_title {
    margin-top: 10px;
  }

  .sub_title span {

    margin-right: 50px;
  }

  .content {
    margin-top: 10px;
    width: 100%;
    overflow: hidden;

    /* 响应式表格 */
    @media (max-width: 768px) {
      table {
        width: 100%;
      }
    }
  }

  .content table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    table-layout: fixed;
    min-width: 0;
    max-width: 100%;
    font-size: 13px;
  }

  .content table th {
    text-align: center;
    border: 1px solid #e4e7ed;
    padding: 12px 8px;
    background-color: #f5f7fa;
    font-weight: 600;
    color: #303133;
    word-break: break-word;
    vertical-align: middle;
  }

  .content table th:first-child {
    text-align: left;
    padding-left: 12px;
    padding-right: 12px;
    white-space: normal;
    word-break: break-word;
    min-width: 160px;
  }

  .content table td {
    text-align: center;
    border: 1px solid #e4e7ed;
    padding: 12px 8px;
    word-break: break-word;
    vertical-align: middle;
    line-height: 1.5;
  }

  .content table td:first-child {
    text-align: left;
    padding-left: 12px;
    padding-right: 12px;
    white-space: normal;
    word-break: break-word;
    min-width: 160px;
  }

  .content table td:not(:first-child) {
    text-align: center;
    padding: 8px 4px;
    font-size: 13px;
  }

  .content table td a {
    color: #409eff;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  .content table td span {
    display: inline-block;
    width: 100%;
    min-height: 20px;
  }

  .content table td:empty::before {
    content: '\00a0';
  }

  .content table tbody tr {
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    &:nth-child(even) {
      background-color: #fafafa;
    }
  }

  .content-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: flex-start;
    align-items: center;
    padding: 4px 0;
    width: 100%;
  }

  .content-item {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f0f9ff;
    border-radius: 4px;
    font-size: 12px;
    white-space: normal;
    word-break: break-word;
    max-width: 100%;

    a {
      color: #409eff;
      text-decoration: none;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #66b1ff;
        text-decoration: underline;
      }
    }
  }
}

.serviceContainer {
  width: 100%;
  margin-bottom: 20px;

  .serviceItem {
    margin-top: 0px;

  }

  .serviceItem .title {
    font-weight: bold;
  }

}

.detail-upload-btn{
  margin: 0px 5px 10px 5px;

}
</style>
