<template>
  <div class="inquiry-config">
    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: 'workbenchList' }"> 综合工作台 </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        <a href="" @click.prevent="goOpportunityDetail" style="color: #999;font-weight: 500;">机会详情</a>
      </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        平安询价
      </el-breadcrumb-item>
    </el-breadcrumb>

    <TableToolTemp :toolListProps="toolListProps" class="log-tool"></TableToolTemp>

    <div class="nav-list">
      <div
        v-for="(item, index) in navBarlist"
        :key="index"
        :class="{ li: true, active: currentIndex == index }"
        :style="{
          color: currentIndex == index ? themeObj.color : '',
          'border-color': currentIndex == index ? themeObj.color : '',
        }"
        @click="navChange(item,index)"
      >
        {{ item.name }}
      </div>
    </div>
    <component :is="currentItem.componentName"></component>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import inquiryAdd from "./components/inquiryAdd";
import inquiryRecord from "./components/inquiryRecord";

export default {
  name: "inquiryConfig",
  provide() {
    // return {
    //   userDetail: this,
    //   opportunityDetailFn: () => this.opportunityDetail,
    //   opportunityFn: () => this.opportunityDetail.opportunity,
    //   enterpriseFn: () => this.opportunityDetail.enterprise,
    //   opportunityProcessLogFn: () => this.opportunityDetail.opportunityProcessLog
    // };
  },
  components: {
    TableToolTemp,
    inquiryAdd,
    inquiryRecord,
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "平安询价",
        toolList: []
      },
      navBarlist: [
        {
          name:"新建询价",
          componentName:"inquiryAdd"
        },
        {
          name:"询价记录",
          componentName:"inquiryRecord"
        },
      ],
      currentItem:{
        name:"新建询价",
        componentName:"inquiryAdd"
      },
      currentIndex:0,
      // 机会完整详情
      opportunityDetail: {
        enterprise: {}, // 客户信息
        opportunity: {}, // 机会基本信息
        opportunityProcessLog: {}, // 机会详细信息
      },
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  watch: {},
  async created() {
    // 获取机会详情数据
    // await this.getOpportunityDetailFullFun();
    // let componentName = this.$route.query.componentName;
    // if(componentName){
    //   this.currentItem.componentName = componentName;
    //   this.currentIndex = this.navBarlist.findIndex(item => item.componentName == componentName);
    // }
  },
  mounted() {

  },
  methods: {
    navChange(item,index) {
      this.currentItem = item;
      this.currentIndex = index
    },

    // 获取机会详情完整信息
    async getOpportunityDetailFullFun() {
      try {
        let res = await getOpportunityDetailFull({
          opportunityId: this.$route.query.id
        });
        if (res) {
          this.opportunityDetail = res;
        }
      } catch (error) {
        console.error('获取机会详情失败:', error);
      }
    },

    // 更新机会详情数据的方法，供子组件调用
    updateOpportunityDetail(newData) {
      this.opportunityDetail = { ...this.opportunityDetail, ...newData };
    },

    goOpportunityDetail(){
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: this.$route.query.opportunityId
        }
      });
    }

  },
};
</script>

<style lang="less">
.inquiry-config {
  .breadcrumb {
    padding: 13px 20px;
    border-bottom: 1px solid #eeeeee;
  }
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    margin-top: 20px;
    margin-bottom: 20px;
    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;
      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}
</style>
