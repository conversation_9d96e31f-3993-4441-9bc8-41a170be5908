<template>
  <div class="user-add">

    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: 'userList' }"> 用户管理 </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }">
        {{popupTitle}}
      </el-breadcrumb-item>
    </el-breadcrumb>

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <div style="margin-left: 50px;">

    <el-form ref="addForm" :model="addData" label-width="120px" :rules="addRules" label-position="right">
<!--      <div style="max-height:70vh;overflow:auto">-->
        <el-form-item label="云服账号" prop="bscUserName">
          <template slot="label">
            <span>云服账号 :</span>
          </template>
          <el-input :disabled="!this.isAdd" v-model="addData.bscUserName" @change="changeInput" auto-complete="off"
                    class="dt-input-width" placeholder="请输入云服账号"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="nickName">
          <template slot="label">
            <span class="item-rule-span">*</span>姓名 :
          </template>
          <el-input v-model="addData.nickName" :disabled="disabled" auto-complete="off" class="dt-input-width"
                    placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <template slot="label">
            <span class="item-rule-span">*</span>手机号 :
          </template>
          <el-input v-model="addData.phone" :disabled="disabled" auto-complete="off" class="dt-input-width"
                    placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <template slot="label">
            <span class="item-rule-span">*</span>邮箱 :
          </template>
          <el-input v-model="addData.email" :disabled="disabled" auto-complete="off" class="dt-input-width"
                    placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="企微账号" prop="wechatAccount">
          <template slot="label">
            企微账号 :
          </template>
          <el-input v-model="addData.wechatAccount" auto-complete="off" class="dt-input-width"
                    placeholder="请输入企微账号"></el-input>
        </el-form-item>
        <el-form-item label="EHR账号" prop="ehrUserCode">
          <template slot="label">
            EHR账号 :
          </template>
          <el-input v-model="addData.ehrUserCode" auto-complete="off" class="dt-input-width"
                    placeholder="请输入EHR账号"></el-input>
        </el-form-item>
        <el-form-item label="大童销售工号" prop="agentCode">
          <template slot="label">
            大童销售工号 :
          </template>
          <el-input v-model="addData.agentCode" auto-complete="off" class="dt-input-width"
                    placeholder="请输入大童销售工号"></el-input>
        </el-form-item>
        <el-form-item label="人员归属" prop="relationType">
          <template slot="label">
            人员归属 :
          </template>
          <el-radio-group v-model="addData.relationType" @change="changeOrganRadio">
            <el-radio v-model="addData.relationType" v-for="(item, index) in relationTypeList" :key="index"
                      :label="item.dicItemCode" :disabled="!isAdd">{{ item.dicItemName }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" prop="selectOrgValue">
          <el-cascader :disabled="addData.relationType == 'out'" :options="organData"
                       :props="organProps" class="dt-cascader" ref="dt-cascader" v-model="selectOrgValue"
                       @change="handleCascader" filterable clearable :key="cascaderKey" placeholder="请选择人员归属"></el-cascader>
        </el-form-item>
        <el-form-item label="机构专家身份" prop="expertType">
          <template slot="label">
            机构专家身份 :
          </template>
          <el-select v-model="addData.expertType" filterable class="dt-input-width" placeholder="请选择机构专家身份" clearable>
            <el-option v-for="(item, index) in expertTypeList" :label="item.dicItemName" :value="item.dicItemCode"
                       :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="擅长险种" prop="insuranceTypes">
          <template slot="label">
            擅长险种 :
          </template>
          <div style="display: flex">
          <div>
          <el-select v-model="selectInsuranceTypes" filterable class="dt-input-width" placeholder="请选择擅长险种" multiple clearable>
            <el-option v-for="(item, index) in insuranceList" :label="item.dicItemName" :value="item.dicItemCode"
                       :key="index">
            </el-option>
          </el-select>
          </div>
          <div style="margin-left:20px;">
            <i class="el-icon-edit" style="margin-right:5px;font-size: 20px;color: #e8900c;"></i>
            <el-button type="text" @click="addOrUpdateInsurance">编辑可选项</el-button>
          </div>
          </div>
        </el-form-item>
        <el-form-item label="擅长行业" prop="industryTypes">
          <template slot="label">
            擅长行业 :
          </template>
          <el-select v-model="selectIndustryTypes" filterable class="dt-input-width" placeholder="请选择擅长行业" multiple clearable>
            <el-option v-for="(item, index) in industryTypesList" :label="item.dicItemName" :value="item.dicItemCode"
                       :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="个人简介" prop="personDesc">
          <template slot="label">
            个人简介 :
          </template>
          <el-input style="width: 81%" type="textarea" v-model="addData.personDesc"
                    :autosize="{ minRows: 5, maxRows: 8 }" placeholder="请输入人员个人简介" maxlength="300字" show-word-limit></el-input>
        </el-form-item>
<!--      </div>-->

      <div class="addPopupFooter">
        <div style="padding:20px 0;text-align: center;">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
                     @click="closeUserPopup">取消</el-button>
          <el-button type="primary" class="btn-width"
                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }" @click="confirm">确认</el-button>
        </div>
      </div>
    </el-form>
    </div>


    <!-- 新增/编辑 -->
    <DtPopup :isShow.sync="showInsurancePopup" @close="closeInsurancePopup" size="mini" title="" :footer="false">
      <TableToolTemp :toolListProps="insuranceToolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>
      <div style="text-align: right;margin-bottom:10px;color:rgb(153, 153, 153);">* 点击其中一行拖动可编辑顺序</div>
      <div style="max-height:70vh;overflow:auto">
        <el-table :data="insuranceTableData" class="dt-table" style="width: 100%" stripe row-key="id">
          <el-table-column align="center" prop="insuranceName" label="选项名称">
            <template slot-scope="scope">
              <span v-if="scope.row.isAdd == '0'">{{scope.row.insuranceName}}</span>
              <el-input v-else v-model="insuranceValue" auto-complete="off" class="dt-input-width"
                        placeholder="请输入险种名称"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="170px">
            <template slot-scope="scope">
              <el-button class="btn-center" type="text" @click="delInsurance(scope.row)" v-if="scope.row.isAdd == '0'">删除</el-button>
              <el-button class="btn-center" type="text" @click="addInsurance(scope.row)" v-if="scope.row.isAdd == '1'">新增</el-button>
              <el-button class="btn-center" type="text" @click="closeInsurance(scope.row)" v-if="scope.row.isAdd == '1'">关闭</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="addPopupFooter">
        <div style="padding:20px 0;text-align: center;">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
                     @click="closeInsurancePopup">关 闭</el-button>
<!--          <el-button type="primary" class="btn-width"-->
<!--                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }" @click="closeInsurancePopup">确认</el-button>-->
        </div>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/userManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { getRolePage } from "@/api/roleManagement/index.js";
import {
  deleteInsurance,
  findInsuranceList,
  findLegalOrgDataByTenantId,
  findRolesByUserId,
  getRoleList
} from "@/api/userManagement/index.js";
import Sortable from 'sortablejs'

export default {
  name: "userAdd",
  data() {
    return {
      toolListProps: {
        toolTitle: "新增人员",
        toolList: []
      },
      insuranceToolListProps:{
        toolTitle: "编辑擅长险种可选项",
        toolList: [
          {
            name: "新增",
            icon: "iconfont icondt8",
          }
        ]
      },
      showDelPopup: false,
      showInsurancePopup: false,
      isAdd: true,
      popupTitle: "",
      showDetailPopup: false,
      stopPopup:false,
      showStatusPopup:false,
      statusTitle: "",
      selectOrgValue: [],
      cascaderKey: 0,
      addData: {
        email:"",
        nickName:"",
        phone:"",
        userId:"",
        relationType: "dept",
        organCode: "",
        organCodePath: "",
        stopReason:"",
        insuranceTypes:"",
        insuranceNames:"",
        industryTypes:"",
        industryNames:"",
        personDesc:""
      },
      statusData: {
        id: "",
        status: "",
        stopReason:""
      },
      addRules: {
        nickName: [{ required: true, validator: validate, trigger: "blur" }],
        phone: [{
          required: true,
          validator: validate,
          trigger: "blur",
          regax: [
            {
              message: "请输入正确的手机号",
              ruleFormat: /^1[3456789]\d{9}$/,
              type: "number"
            }
          ]
        }],
        email: [{
          required: true,
          validator: validate,
          trigger: "blur",
          regax: [
            {
              message: "请输入正确的邮箱格式",
              ruleFormat:
                "/^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((\\.[a-zA-Z0-9_-]{2,3}){1,2})$/"
            }
          ]
        }]
      },
      roleList: [],
      expertTypeList: [],
      insuranceList: [],
      insuranceTableData:[],
      showAddComponent:false,
      industryTypesList: [],
      relationTypeList: [],
      disabled: true,
      organData: [],
      organProps: {
        label: 'orgName',
        value: 'orgCode',
        children: 'children',
        expandTrigger: 'hover',
        checkStrictly: true
      },
      showRole:false,
      selectionRoleSwitch:false,
      roleTableData:[],
      roleInitParam:{
        pageNum: 1,
        pageSize: 10,
        param: {
          userId:"",
        }
      },
      roleTotal:0,
      nickName:"",
      belongOrgNamePath:"",
      roleName:"",
      orgAddData: {
        orgType: "org",
        userOrgs: [],
        userId: "",
        tenantId: ""
      },
      orgProps:{
        label: 'orgName',
        children: 'children'
      },
      legalOrgList:[],
      showOrg:false,
      checkedOrgList: [],
      selectInsuranceTypes:[],
      selectIndustryTypes:[],
      deleteData:{
        userId:""
      },
      insuranceValue:"",
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    }
  },
  filters: {
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      // let roleListTemp = await api.getRoleList({});
      // 机构专家身份
      let expertTypeListTemp = await getDicItemList("elms.expert.type");
      expertTypeListTemp.forEach(item => {
        this.expertTypeList.push({
          dicItemName: item.dicItemName,
          dicItemCode: parseInt(item.dicItemCode)
        });
      });
      // 状态
      // await getDicItemList("sys.gateway.status");
      this.relationTypeList = await getDicItemList("elms.user.belong.type");

      // this.legalOrgList = await api.findLegalOrgData({});

    },
    async initData() {
      if (this.$route.query.id && this.$route.query.id != '') {
        this.popupTitle = "修改人员";
        this.isAdd = false;
        let res = await api.detail({id: this.$route.query.id, tenantId: this.$route.query.tenantId});
        this.addData = this._.cloneDeep(res);
        await this.loadSelectData();
      } else {
        await this.getDeptTree();
      }
      await this.initIndustryData();
      await this.initInsuranceData();
    },
    async handleTool(item) {
      if (item.name == "新增") {
        if (!this.showAddComponent) {
          this.showAddComponent = true;
          this.insuranceValue = "";
          this.insuranceTableData.push(
            {id: "", name: "", isAdd: "1"}
          );
        }
      }
    },
    // 查询擅长行业
    async initIndustryData() {
      let res = await api.findIndustryList({"level": 2});
      if (res) {
        // 擅长行业
        res.forEach(item => {
          this.industryTypesList.push({
            dicItemName: item.name,
            dicItemCode: item.code
          });
        });
      }
    },

    // 查询擅长险种
    async initInsuranceData() {
      let res = await api.findInsuranceList({});
      if (res && Array.isArray(res)) {
        this.insuranceList = [];
        // 擅长行业
        res.forEach(item => {
          this.insuranceList.push({
            dicItemName: item.insuranceName,
            dicItemCode: item.insuranceName
          });
        });
      }
    },

    closeUserPopup() {
      // this.selectOrgValue = [];
      // // this.organData = [];
      // // this.getDeptTree();
      // this.cascaderKey = 0;
      // this.showPopup = false;
      // this.disabled = true;
      // this.$nextTick(() => {
      //   this.$refs.addForm.clearValidate();
      // });
      this.$router.push({
        name:"userList",
        query:{}
      });
    },

    async view(row) {
      let res = await api.detail({ id: row.id,tenantId:this.tenantId });
      this.addData = this._.cloneDeep(res);
      // this.selectOrgValue = this.addData.organCodePath.split("/");
      await this.loadSelectData();
      await this.initIndustryData();
      this.showDetailPopup = true;
    },
    async update(row) {
      await this.initIndustryData();
      this.isAdd = false;
      this.popupTitle = "修改人员";
      let res = await api.detail({ id: row.id,tenantId:this.tenantId });
      this.addData = this._.cloneDeep(res);
      await this.loadSelectData();
      // if (this.addData.relationType == 'dept') {
      //   await this.getDeptTree();
      // } else {
      //   await this.getOrganTree();
      // }
      // this.selectIndustryTypes = [];
      // this.selectInsuranceTypes = [];
      //
      // this.selectOrgValue = this.addData.organCodePath.split("/");
      // if (res.insuranceTypes && res.insuranceTypes.length > 0) {
      //   this.selectInsuranceTypes = this.addData.insuranceTypes.split(",");
      // }
      // if (res.industryTypes && res.industryTypes.length > 0) {
      //   this.selectIndustryTypes = this.addData.industryTypes.split(",");
      // }
      this.showPopup = true;
    },
    async loadSelectData() {
      this.selectIndustryTypes = [];
      this.selectInsuranceTypes = [];

      if (this.addData.relationType == 'dept') {
        await this.getDeptTree();
      } else if (this.addData.relationType == 'org') {
        await this.getOrganTree();
      }
      if(this.addData.organCodePath && this.addData.organCodePath !='') {
        this.selectOrgValue = this.addData.organCodePath.split("/");
      }

      if (this.addData.insuranceTypes && this.addData.insuranceTypes !='') {
        this.selectInsuranceTypes = this.addData.insuranceTypes.split(",");
      }
      if (this.addData.industryTypes && this.addData.industryTypes !='') {
        this.selectIndustryTypes = this.addData.industryTypes.split(",");
      }
    },
    async confirm() {

      if (!validateAlls(this.$refs.addForm)) { return; }

      if(this.addData.relationType !='out') {
        if(this.addData.organCode == '') {
          this.$message.error("人员归属机构/部门不能为空");
          return;
        }
      }
      let insuranceTypesArr = [];
      let insuranceNamesArr = [];
      if (this.selectInsuranceTypes.length > 0) {
        this.addData.insuranceTypes = this.selectInsuranceTypes.join(",");
        this.addData.insuranceNames = this.selectInsuranceTypes.join(",");
      }

      let industryTypesArr = [];
      let industryNamesArr = [];
      if(this.selectIndustryTypes.length > 0) {
        this.selectIndustryTypes.forEach(code => {
          let op = _.find(this.industryTypesList, item => {
            return item.dicItemCode == code;
          });
          if(op) {
            industryNamesArr.push(op.dicItemName);
          }
        });
        this.addData.industryTypes = this.selectIndustryTypes.join(",");
        this.addData.industryNames = industryNamesArr.join(",");
      }
      let param = this._.cloneDeep(this.addData);
      param.tenantId = this.tenantId;
      // if (this.selectOrgValue.length > 0) {
      //   param.organCode = this.selectOrgValue[this.selectOrgValue.length - 1];
      // } else {
      //   param.organCode = "";
      // }
      let res = await api.saveOrUpdate(param);
      console.log(res,"-----------------------")
      if (res) {
        this.$message({
          type: "success",
          message: this.isAdd ? "新增成功" : "修改成功"
        });
        this.$router.push({
          name:"userList",
        });
      }
    },
    async changeInput(val) {
      if (this.isAdd) {
        if (!val || val == '') {
          this.disabled = true;
          return;
        }
        let res = await api.findByBscUserName({ bscUserName: val, tenantId: this.tenantId });
        if (res && res.userId) {
          this.addData.email = res.email;
          this.addData.userId = res.userId;
          this.addData.nickName = res.nickName;
          this.addData.phone = res.phone;
          this.addData.personDesc = res.personDesc;
          if (res.ehrUserCode) {
            this.addData.ehrUserCode = res.ehrUserCode
          }
          if (res.agentCode) {
            this.addData.agentCode = res.agentCode;
          }

          if (res.insuranceTypes && res.insuranceTypes.length > 0) {
            this.selectInsuranceTypes = res.insuranceTypes.split(",");
          }
          if (res.industryTypes && res.industryTypes.length > 0) {
            this.selectIndustryTypes = res.industryTypes.split(",");
          }
          this.disabled = true;
        } else {
          this.addData = {
            ..._.cloneDeep(this.$options.data().addData),
            bscUserName: val
          }
          this.disabled = true;
        }
      }
    },
    async changeOrganRadio(val) {
      ++this.cascaderKey;
      this.selectOrgValue = [];
      if (val == "org") {
        await this.getOrganTree();
      } else if (val == "dept") {
        await this.getDeptTree();
      }
    },
    async getOrganTree() {
      this.organData = [];
      let res = await api.findOrganizationData({});
      if (res) {
        let treeData = _.cloneDeep(res);
        this.organData = treeData;
      }
    },
    async getDeptTree() {
      this.organData = [];
      let res = await api.findDepartmentData({});
      if (res) {
        let treeData = _.cloneDeep(res);
        this.organData = treeData;
      }
    },
    handleCascader(name, val) {
      let dtCascader = this.$refs["dt-cascader"]
      this.$nextTick(() => {
        let textArr = [];
        let arr = dtCascader.getCheckedNodes()[0].pathNodes;
        arr.forEach((i) => {
          textArr.push(i.label)
        });
        this.addData.organCode = name[name.length - 1];
        this.addData.organName = textArr[textArr.length - 1];
        this.addData.organCodePath = this.selectOrgValue.join("/")
        this.addData.organNamePath = textArr.join("/");
      })

    },
    async handleSelectionChange(val,op) {
      if (!this.selectionRoleSwitch) return;
      if(val && val.length > 0) {
        let roleIds = [];
        val.forEach(item => {
          roleIds.push(item.id);
        });
        let res = await api.addUserRole({
          roleIds:roleIds,
          userId:this.roleInitParam.param.userId
        });
      } else if (val.length == 0) {// 取消全选
        let roleIds = this.roleTableData.map(item => {
          return item.id;
        });
        let res = await api.deleteUserRole({
          roleIds:roleIds,
          userId:this.roleInitParam.param.userId
        });
      }
    },
    async chooseRoleCheck(data, row) {
      this.selectionRoleSwitch = false;
      let isSelected = false;
      if(data.length > 0) {
        data.forEach(item => {
          if(item.id == row.id) {
            isSelected = true;
          }
        });
      }
      if (isSelected) {
        let res = await api.addUserRole({
          roleId: row.id,
          userId: this.roleInitParam.param.userId
        });
      } else {
        let res = await api.deleteUserRole({
          roleId: row.id,
          userId: this.roleInitParam.param.userId
        });
      }
    },
    handleRoleSizeChange(val) {
      this.selectionRoleSwitch = false;
      this.roleInitParam.pageSize = val;
      this.getRolePage();
    },
    handleRoleCurrentChange(val) {
      this.selectionRoleSwitch = false;
      this.roleInitParam.pageNum = val;
      this.getRolePage();
    },
    cancelRole() {
      this.selectionRoleSwitch = false;
      this.showRole = false;
      this.roleInitParam.pageNum = 1;
      this.roleInitParam.pageSize = 10;
      this.initData();
    },
    async showOrgAuthPopup(row) {
      this.nickName = row.nickName;
      this.belongOrgNamePath = row.organNamePath;
      this.roleName = row.roleName;
      this.orgAddData.userId = row.userId;
      if (row.orgType) {
        this.orgAddData.orgType = row.orgType;
      }

      this.checkedOrgList = [];
      if (!row.orgType || row.orgType == 'org') {
        // this.legalOrgList = await api.findLegalOrgData({});
        let res = await api.getCheckedUserOrg({
          userId: this.orgAddData.userId
        });
        if (res && res instanceof Array) {
          this.checkedOrgList = res;
        }
        setTimeout(() => {
          this.$refs.orgTree.setCheckedKeys(this.checkedOrgList);
        }, 100);
      }
      this.showOrg = true;
    },
    cancelOrg(){
      this.showOrg = false;
    },
    async saveUserOrg() {
      if (this.orgAddData.orgType == 'org') {
        let checkedNodes = this.$refs.orgTree.getCheckedNodes(true);
        if (checkedNodes.length == 0) {
          this.$message.error("机构不能为空");
          return;
        }
        this.orgAddData.userOrgs = checkedNodes;
      }
      this.orgAddData.tenantId = this.tenantId;
      let res = await api.saveOrUpdateUserOrg(this.orgAddData);
      if (res) {
        this.$message.success("配置成功");
      }
      this.showOrg = false;
      this.initData();
    },
    async changeOrgTypeRadio(val) {
      if (val == 'org') {
        this.legalOrgList = await api.findLegalOrgData({});
      }
    },

    async addOrUpdateInsurance() {
      let res = await api.findInsuranceList({});
      if (res && Array.isArray(res)) {
        this.insuranceTableData = [];
        this.insuranceTableData = res;
        if (this.insuranceTableData.length == 0) {
          this.insuranceTableData.push({id: "", name: "", isAdd: "1"});
        } else {
        }
        this.$nextTick(() => {
          this.rowDrop();
        });
      }
      this.showInsurancePopup = true;
    },
    async rowDrop() {
      const tbody = document.querySelector('.el-table__body-wrapper tbody');

      let that = this;
      Sortable.create(tbody, {
        onStart: (item) => {
          console.log(item,'-----onStart----');
          console.log(that.showAddComponent,'-----showAddComponent----');
          if(that.showAddComponent) {
            // that.$message.error("新增状态不能拖拽");
            // 新增状态时，解除新增，实现拖拽排序
            that.removeAddComponent();
            that.showAddComponent = false;
            // return;
          }
        },
        onEnd({newIndex, oldIndex}) {
            const currentRow = that.insuranceTableData.splice(oldIndex, 1)[0];
            that.insuranceTableData.splice(newIndex, 0, currentRow);
            that.updateInsuranceSort();
        }
      });
    },
    async updateInsuranceSort() {
      let updateData = {
        "list": this.insuranceTableData
      }
      let res = await api.updateInsuranceSort(updateData);
      if (res) {
        await this.reloadInsuranceTableData();
      }
    },
    // 关闭险种弹窗
    async closeInsurancePopup(){
      await this.initInsuranceData();
      this.showInsurancePopup = false;
      this.showAddComponent = false;
    },
    // 删除险种
    async delInsurance(row){
      const index = this.insuranceTableData.findIndex(item => item.insuranceId === row.insuranceId)
      if (index > -1) {
        this.insuranceTableData.splice(index, 1)
      }

      if(this.selectInsuranceTypes.length > 0) {
        const index = this.selectInsuranceTypes.findIndex(item => item === row.insuranceName)
        if (index > -1) {
          this.selectInsuranceTypes.splice(index, 1)
        }
      }

      let res = api.deleteInsurance({"insuranceId":row.insuranceId});
      if(res) {
        this.$message.success("删除成功");
      }
    },
    async addInsurance() {
      if (this.insuranceValue == '') {
        this.$message.error("险种名称不能为空");
        return;
      }
      let addData = {
        "insuranceName": this.insuranceValue,
        "sort": this.insuranceTableData.length,
      }
      let res = await api.saveInsurance(addData);
      if (res) {
        // await this.initInsuranceData();
        // 新增完成删除该组件
        this.removeAddComponent();
        // const index = this.insuranceTableData.findIndex(item => item.isAdd === '1')
        // if (index > -1) {
        //   this.insuranceTableData.splice(index, 1)
        // }
        await this.reloadInsuranceTableData();
        // 不显示新增组件
        this.showAddComponent = false;
        // this.$nextTick(() => {
        //   this.rowDrop();
        // });
        this.$message.success("新增成功");
      }
    },
    // 关闭新增险种控件
    closeInsurance(){
      this.showAddComponent = false;
      this.removeAddComponent();
    },
    removeAddComponent() {
      // 新增完成删除该组件
      const index = this.insuranceTableData.findIndex(item => item.isAdd === '1')
      if (index > -1) {
        this.insuranceTableData.splice(index, 1)
      }
    },
    async reloadInsuranceTableData(){
      let res = await api.findInsuranceList({});
      if (res){
        this.insuranceTableData = [];
        this.$nextTick(() => {
          this.insuranceTableData = res;
        });
      }
    },

  }
};
</script>
<style lang="less">
.user-add {

  .item-rule-span {
    color: red;
    margin-right: 8px;
  }

  .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
    display: none;
  }

  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }

  }
}

// 启用禁用弹窗样式
.status-confirm {
  padding: 0 20px 20px 20px;

  .confirm-content {
    margin-bottom: 20px;
    text-align: center;

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .confirm-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}

.check-popup {
  width: 100%;

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;

    .btn-width {
      width: 120px;
    }
  }

  .end-exam-text {
    margin-top: 10px;
  }
}

.roleDialog {
  .roleHeader {
    display: flex;
    margin-bottom: 15px;
    height: 38px;
    line-height: 38px;
    padding-left: 40px;
  }

  .roleFooter {
    text-align: center;
    padding: 30px 0;
    button {
      width: 180px;
    }
  }

  .el-dialog {
    padding: 0;
  }

  .roleDialogWrap {
    max-height: 45vh;
    overflow: auto;
  }

}

.orgDialog {
  .orgHeader {
    display: flex;
    margin-bottom: 15px;
    height: 38px;
    line-height: 38px;
    padding-left: 40px;
  }

  .orgFooter {
    text-align: center;
    padding: 30px 0;
    button {
      width: 180px;
    }
  }

  .orgDialogWrap {
    margin-bottom: 14px;
  }

}

.form-block {
  padding-left: 0px;
}
</style>
