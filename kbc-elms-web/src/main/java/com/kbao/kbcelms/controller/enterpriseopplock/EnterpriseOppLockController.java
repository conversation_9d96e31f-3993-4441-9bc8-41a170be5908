package com.kbao.kbcelms.controller.enterpriseopplock;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockAddDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockUpdateDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.OpportunityLockDTO;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockService;
import com.kbao.kbcelms.enterpriseopplock.vo.EnterpriseOppLockVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 企业机会锁定Controller
 * <AUTHOR>
 * @date 2025-01-15
 */
@Api(tags = "企业机会锁定管理")
@RestController
@RequestMapping("/enterpriseOppLock")
public class EnterpriseOppLockController {
    
    @Autowired
    private EnterpriseOppLockService enterpriseOppLockService;
    
    /**
     * 分页查询企业机会锁定列表
     */
    @ApiOperation(value = "分页查询企业机会锁定列表", notes = "支持多种查询条件的分页查询")
    @PostMapping("/page")
    public Result<PageInfo<EnterpriseOppLockVO>> getPageList(@RequestBody PageRequest<EnterpriseOppLockQueryDTO> pageRequest) {
        return enterpriseOppLockService.getPageList(pageRequest);
    }
    
    /**
     * 根据ID查询企业机会锁定详情
     */
    @ApiOperation(value = "查询企业机会锁定详情", notes = "根据ID查询单条记录详情")
    @GetMapping("/{id}")
    public Result<EnterpriseOppLockVO> getById(@ApiParam(value = "主键ID", required = true) @PathVariable Integer id) {
        return enterpriseOppLockService.getById(id);
    }
    
    /**
     * 新增企业机会锁定
     */
    @ApiOperation(value = "新增企业机会锁定", notes = "创建新的企业机会锁定记录")
    @PostMapping("/add")
    public Result<Integer> add(@Valid @RequestBody EnterpriseOppLockAddDTO addDTO) {
        return enterpriseOppLockService.add(addDTO);
    }
    
    /**
     * 更新企业机会锁定
     */
    @ApiOperation(value = "更新企业机会锁定", notes = "更新现有的企业机会锁定记录")
    @PostMapping("/update")
    public Result<Boolean> update(@Valid @RequestBody EnterpriseOppLockUpdateDTO updateDTO) {
        return enterpriseOppLockService.update(updateDTO);
    }
    
    /**
     * 删除企业机会锁定
     */
    @ApiOperation(value = "删除企业机会锁定", notes = "逻辑删除企业机会锁定记录")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@ApiParam(value = "主键ID", required = true) @PathVariable Integer id) {
        return enterpriseOppLockService.deleteById(id);
    }
    
    /**
     * 根据机会ID查询锁定记录
     */
    @ApiOperation(value = "根据机会ID查询锁定记录", notes = "查询指定机会下的所有企业锁定记录")
    @GetMapping("/opportunity/{opportunityId}")
    public Result<List<EnterpriseOppLockVO>> getByOpportunityId(
            @ApiParam(value = "机会ID", required = true) @PathVariable Integer opportunityId) {
        return enterpriseOppLockService.getByOpportunityId(opportunityId);
    }
    
    /**
     * 根据社会统一信用代码查询锁定记录
     */
    @ApiOperation(value = "根据信用代码查询锁定记录", notes = "查询指定企业的所有机会锁定记录")
    @GetMapping("/creditCode/{creditCode}")
    public Result<List<EnterpriseOppLockVO>> getByCreditCode(
            @ApiParam(value = "社会统一信用代码", required = true) @PathVariable String creditCode) {
        return enterpriseOppLockService.getByCreditCode(creditCode);
    }
    
    /**
     * 检查企业是否被锁定
     */
    @ApiOperation(value = "检查企业锁定状态", notes = "检查指定企业在指定机会下是否被锁定")
    @GetMapping("/check/{opportunityId}/{creditCode}")
    public Result<Boolean> checkLocked(
            @ApiParam(value = "机会ID", required = true) @PathVariable Integer opportunityId,
            @ApiParam(value = "社会统一信用代码", required = true) @PathVariable String creditCode) {
        return enterpriseOppLockService.checkLocked(opportunityId, creditCode);
    }
    
    /**
     * 批量锁定企业机会
     */
    @ApiOperation(value = "批量锁定企业机会", notes = "批量创建企业机会锁定记录")
    @PostMapping("/batchAdd")
    public Result<String> batchAdd(@Valid @RequestBody List<EnterpriseOppLockAddDTO> addDTOList) {
        try {
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            
            for (EnterpriseOppLockAddDTO addDTO : addDTOList) {
                try {
                    Result<Integer> result = enterpriseOppLockService.add(addDTO);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMsg.append("企业[").append(addDTO.getName()).append("]锁定失败：")
                            .append(e.getMessage()).append("; ");
                }
            }
            
            String message = String.format("批量锁定完成，成功%d条，失败%d条", successCount, failCount);
            if (failCount > 0) {
                message += "。失败原因：" + errorMsg.toString();
            }
            
            return Result.succeed(message);
        } catch (Exception e) {
            return Result.failed("批量锁定失败：" + e.getMessage());
        }
    }

    /**
     * 清理过期的锁定记录
     */
    @ApiOperation(value = "清理过期的锁定记录", notes = "清理所有已过期的企业机会锁定记录")
    @PostMapping("/cleanExpired")
    public Result<Integer> cleanExpiredLocks() {
        return enterpriseOppLockService.cleanExpiredLocks();
    }
    
    /**
     * 锁定机会
     */
    @ApiOperation(value = "锁定机会", notes = "根据机会ID进行机会锁定操作，包括检查锁定状态、创建锁定记录、更新机会状态等")
    @PostMapping("/lockOpportunity")
    public Result<String> lockOpportunity(@Valid @RequestBody OpportunityLockDTO lockDTO) {
        return enterpriseOppLockService.lockOpportunity(lockDTO);
    }
    
    /**
     * 解锁机会
     */
    @ApiOperation(value = "解锁机会", notes = "根据机会ID进行机会解锁操作，删除锁定记录并更新机会状态")
    @PostMapping("/unlockOpportunity/{opportunityId}")
    public Result<String> unlockOpportunity(@ApiParam(value = "机会ID", required = true) @PathVariable Integer opportunityId) {
        return enterpriseOppLockService.unlockOpportunity(opportunityId);
    }
}
