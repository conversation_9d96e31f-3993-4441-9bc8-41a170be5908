package com.kbao.kbcelms.controller.enterpriseopplock;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordAddDTO;
import com.kbao.kbcelms.enterpriseopplock.dto.EnterpriseOppLockRecordQueryDTO;
import com.kbao.kbcelms.enterpriseopplock.service.EnterpriseOppLockRecordService;
import com.kbao.kbcelms.enterpriseopplock.vo.EnterpriseOppLockRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 企业机会锁定记录Controller
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/enterprise/opp/lock/record")
@Api(tags = "企业机会锁定记录管理")
public class EnterpriseOppLockRecordController extends BaseController {

    @Autowired
    private EnterpriseOppLockRecordService enterpriseOppLockRecordService;

    /**
     * 分页查询企业机会锁定记录列表
     */
    @ApiOperation(value = "分页查询企业机会锁定记录列表", notes = "根据条件分页查询企业机会锁定记录列表")
    @PostMapping("/page")
    @LogAnnotation(module = "企业机会锁定记录", recordRequestParam = true, action = "查询", desc = "分页查询企业机会锁定记录列表")
    public Result<PageInfo<EnterpriseOppLockRecordVO>> page(@Valid @RequestBody PageRequest<EnterpriseOppLockRecordQueryDTO> pageRequest) {
        return enterpriseOppLockRecordService.getPageList(pageRequest);
    }

    /**
     * 根据ID查询企业机会锁定记录详情
     */
    @ApiOperation(value = "查询企业机会锁定记录详情", notes = "根据ID查询企业机会锁定记录详情")
    @GetMapping("/{id}")
    public Result<EnterpriseOppLockRecordVO> getById(@ApiParam(value = "主键ID", required = true) @PathVariable String id) {
        return enterpriseOppLockRecordService.getById(id);
    }

    /**
     * 新增企业机会锁定记录
     */
    @ApiOperation(value = "新增企业机会锁定记录", notes = "创建新的企业机会锁定记录")
    @PostMapping
    @LogAnnotation(module = "企业机会锁定记录", recordRequestParam = true, action = "新增", desc = "新增企业机会锁定记录")
    public Result<String> add(@Valid @RequestBody EnterpriseOppLockRecordAddDTO addDTO) {
        return enterpriseOppLockRecordService.add(addDTO);
    }

    /**
     * 删除企业机会锁定记录
     */
    @ApiOperation(value = "删除企业机会锁定记录", notes = "逻辑删除企业机会锁定记录")
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "企业机会锁定记录", recordRequestParam = true, action = "删除", desc = "删除企业机会锁定记录")
    public Result<Boolean> delete(@ApiParam(value = "主键ID", required = true) @PathVariable String id) {
        return enterpriseOppLockRecordService.deleteById(id);
    }

    /**
     * 根据锁定主表ID查询记录列表
     */
    @ApiOperation(value = "查询锁定记录列表", notes = "根据锁定主表ID查询所有相关的锁定记录")
    @GetMapping("/lock/{lockId}")
    public Result<List<EnterpriseOppLockRecordVO>> getByLockId(@ApiParam(value = "锁定主表ID", required = true) @PathVariable Integer lockId) {
        return enterpriseOppLockRecordService.getByLockId(lockId);
    }

    /**
     * 根据锁定主表ID查询最新有效记录
     */
    @ApiOperation(value = "查询最新有效记录", notes = "根据锁定主表ID查询最新有效的锁定记录")
    @GetMapping("/lock/{lockId}/latest")
    public Result<EnterpriseOppLockRecordVO> getLatestValidByLockId(@ApiParam(value = "锁定主表ID", required = true) @PathVariable Integer lockId) {
        return enterpriseOppLockRecordService.getLatestValidByLockId(lockId);
    }

    /**
     * 根据锁定主表ID批量查询最新有效记录
     */
    @ApiOperation(value = "批量查询最新有效记录", notes = "根据锁定主表ID列表批量查询最新有效的锁定记录")
    @PostMapping("/batch/latest")
    public Result<List<EnterpriseOppLockRecordVO>> getLatestValidByLockIds(@Valid @RequestBody List<Integer> lockIds) {
        return enterpriseOppLockRecordService.getLatestValidByLockIds(lockIds);
    }

    /**
     * 设置记录状态为无效
     */
    @ApiOperation(value = "设置记录无效", notes = "根据锁定主表ID将相关的有效记录设置为无效状态")
    @PutMapping("/lock/{lockId}/invalidate")
    @LogAnnotation(module = "企业机会锁定记录", recordRequestParam = true, action = "修改", desc = "设置记录状态为无效")
    public Result<Boolean> invalidateByLockId(@ApiParam(value = "锁定主表ID", required = true) @PathVariable Integer lockId) {
        return enterpriseOppLockRecordService.invalidateByLockId(lockId);
    }
}
